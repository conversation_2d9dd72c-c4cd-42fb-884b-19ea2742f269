baseURL = "https://hdcode.dev"
languageCode = "en-us"
title = "HDCode"
theme = "hdcode-premium"

# Performance optimizations
enableRobotsTXT = true
minify = true

# Sitemap configuration
[sitemap]
  changefreq = "monthly"
  priority = 0.5

# Security headers
[security]
  [security.exec]
    allow = ['^dart-sass-embedded$', '^go$', '^npx$', '^postcss$']
  [security.funcs]
    getenv = ['^HUGO_']

# Markup configuration
[markup]
  [markup.goldmark]
    [markup.goldmark.renderer]
      unsafe = true

# Main menu configuration
[menu]
  [[menu.main]]
    name = "Home"
    url = "/"
    weight = 1
  [[menu.main]]
    name = "Services"
    url = "/#services"
    weight = 2
  [[menu.main]]
    name = "Expertise"
    url = "/#experience"
    weight = 3
  [[menu.main]]
    name = "About"
    url = "/#about"
    weight = 4
  [[menu.main]]
    name = "Contact"
    url = "/#contact"
    weight = 5

# Site parameters
[params]
  description = "Premium digital solutions for ambitious businesses. We craft exceptional mobile applications and websites that drive growth and establish market leadership."
  email = "<EMAIL>"
  author = "HDCode"
  keywords = "android development, websites, mobile apps, kotlin development, premium digital solutions"

  # Global Stats (reused across sections)
  [params.global_stats]
    projects_completed = "30+"
    client_satisfaction = "98%"
    years_experience = "10+"
    happy_clients = "25+"

  # Hero Section
  [params.hero]
    badge = "Premium Digital Solutions"
    title_line1 = "Crafting"
    title_line2 = "Digital Excellence"
    title_line3 = "with HDCode"
    description = "Helping small and medium businesses enter the digital world through powerful, custom-built software solutions."
    primary_button_text = "Start Your Project"
    secondary_button_text = "View Our Work"

    # Hero Stats (using global stats)
    [[params.hero.stats]]
      number_key = "projects_completed"
      label = "Projects Delivered"
    [[params.hero.stats]]
      number_key = "client_satisfaction"
      label = "Client Satisfaction"
    [[params.hero.stats]]
      number_key = "years_experience"
      label = "Years Experience"



  # Services Section
  [params.services]
    badge = "Our Expertise"
    title = "Premium Services"
    description = "Transform your business with our world-class digital solutions. Each service is crafted to deliver exceptional ROI and market leadership."
    learn_more_text = "Get Started"

    [[params.services.items]]
      icon = "fas fa-laptop-code"
      title = "Premium Websites"
      description = "Enterprise-grade websites that convert visitors into customers. Built with cutting-edge technologies for maximum performance, SEO dominance, and user engagement."
      features = ["Conversion-Optimized Design", "Advanced SEO Strategy", "Lightning-Fast Performance", "Mobile-First Architecture", "Analytics & Tracking", "24/7 Monitoring"]
      featured = false

    [[params.services.items]]
      icon = "fas fa-mobile-alt"
      title = "Android Development"
      description = "Native Android applications that dominate app stores and drive user engagement. Built with modern Kotlin and Material Design for exceptional user experiences."
      features = ["Native Android Performance", "Kotlin & Jetpack Compose", "Material Design 3", "Play Store Optimization", "Push Notifications", "Analytics Integration"]
      featured = true
      badge = "Most Popular"

    [[params.services.items]]
      icon = "fas fa-rocket"
      title = "MVP Development"
      description = "Validate your billion-dollar idea with a market-ready MVP. Get to market 3x faster with our proven development methodology and investor-ready prototypes."
      features = ["Rapid Market Entry", "Investor-Ready Quality", "Scalable Architecture", "User Testing & Feedback", "Iterative Development", "Growth Analytics"]
      featured = false

  # Testimonials Section
  [params.testimonials]
    badge = "Client Success Stories"
    title = "What Our Clients Say"
    description = "Don't just take our word for it. Here's what industry leaders and successful entrepreneurs say about working with HDCode."

    [[params.testimonials.items]]
      name = "Sarah Chen"
      position = "CEO, TechStart Solutions"
      company = "TechStart Solutions"
      avatar = "/images/testimonials/sarah-chen.jpg"
      rating = 5
      text = "HDCode transformed our vision into a market-leading mobile app. Their expertise in Android development and attention to detail exceeded our expectations. We saw a 300% increase in user engagement within the first month."
      project = "FinTech Mobile App"
      result = "300% increase in user engagement"

    [[params.testimonials.items]]
      name = "Michael Rodriguez"
      position = "Founder, GreenTech Innovations"
      company = "GreenTech Innovations"
      avatar = "/images/testimonials/michael-rodriguez.jpg"
      rating = 5
      text = "Working with HDCode was a game-changer for our startup. They delivered a premium website that not only looks stunning but converts visitors into customers. Our conversion rate improved by 250%."
      project = "E-commerce Platform"
      result = "250% conversion rate improvement"

    [[params.testimonials.items]]
      name = "Dr. Emily Watson"
      position = "Director, HealthCare Plus"
      company = "HealthCare Plus"
      avatar = "/images/testimonials/emily-watson.jpg"
      rating = 5
      text = "The MVP HDCode developed for us secured $2M in Series A funding. Their technical expertise and business understanding helped us validate our concept and attract top-tier investors."
      project = "Healthcare MVP"
      result = "$2M Series A funding secured"

  # Trust Signals Section
  [params.trust_signals]
    badge = "Trusted By Industry Leaders"
    title = "Join 100+ Successful Companies"
    description = "From startups to enterprises, we've helped businesses across industries achieve digital excellence"

    [[params.trust_signals.metrics]]
      number = "100+"
      label = "Projects Delivered"
      icon = "fas fa-rocket"

    [[params.trust_signals.metrics]]
      number = "98%"
      label = "Client Satisfaction"
      icon = "fas fa-heart"

    [[params.trust_signals.metrics]]
      number = "$50M+"
      label = "Client Revenue Generated"
      icon = "fas fa-chart-line"

    [[params.trust_signals.metrics]]
      number = "24/7"
      label = "Support Available"
      icon = "fas fa-headset"

    # Trust Badges
    [[params.trust_signals.badges]]
      name = "Google Partner"
      icon = "fab fa-google"
      description = "Certified Google development partner"

    [[params.trust_signals.badges]]
      name = "ISO 27001"
      icon = "fas fa-shield-check"
      description = "Information security certified"

    [[params.trust_signals.badges]]
      name = "GDPR Compliant"
      icon = "fas fa-user-shield"
      description = "Full GDPR compliance guaranteed"

    [[params.trust_signals.badges]]
      name = "24/7 Support"
      icon = "fas fa-clock"
      description = "Round-the-clock technical support"

  # Experience Domains Section
  [params.experience]
    badge = "Industry Experience"
    title = "Domains of Expertise"
    description = "We bring deep experience across diverse industries, delivering tailored solutions that understand your unique challenges"

    [[params.experience.domains]]
      icon = "fas fa-coins"
      name = "Cryptocurrency / Blockchain"
      color = "#f59e0b"

    [[params.experience.domains]]
      icon = "fas fa-heartbeat"
      name = "Healthcare / Medical / Utility"
      color = "#10b981"

    [[params.experience.domains]]
      icon = "fas fa-chart-line"
      name = "Sales / Business / Productivity"
      color = "#3b82f6"

    [[params.experience.domains]]
      icon = "fas fa-calendar-alt"
      name = "Event Management / Entertainment / Media"
      color = "#ec4899"

    [[params.experience.domains]]
      icon = "fas fa-shield-alt"
      name = "Security / Privacy / Networking"
      color = "#8b5cf6"

    [[params.experience.domains]]
      icon = "fas fa-users"
      name = "Social Media / Entertainment / Gamification"
      color = "#06b6d4"

    [[params.experience.domains]]
      icon = "fas fa-car"
      name = "Automotive / Health & Fitness / Tracking"
      color = "#f97316"

    [[params.experience.domains]]
      icon = "fas fa-graduation-cap"
      name = "Education / eLearning"
      color = "#10b981"

    [[params.experience.domains]]
      icon = "fas fa-vote-yea"
      name = "Civic Engagement / Social Impact / Utility"
      color = "#3b82f6"

  # Portfolio Section
  [params.portfolio]
    enabled = false  # Feature flag to show/hide the featured projects section
    badge = "Our Work"
    title = "Featured Projects"
    description = "Discover our latest projects and see how we've helped businesses achieve their digital goals"
    cta_text = "View All Projects"

    [[params.portfolio.items]]
      icon = "fas fa-shopping-cart"
      preview_title = "E-Commerce"
      preview_subtitle = "Website"
      title = "E-Commerce Website"
      description = "Fast and secure e-commerce website with modern design, optimized performance, and seamless user experience."
      technologies = ["Hugo", "HTML/CSS", "JavaScript", "Payment Integration"]
      demo_link = "#"
      github_link = "#"

    [[params.portfolio.items]]
      icon = "fas fa-mobile-alt"
      preview_title = "Banking"
      preview_subtitle = "Android App"
      title = "Android Banking App"
      description = "Secure financial management Android application with biometric authentication, real-time transactions, and comprehensive financial tools."
      technologies = ["Kotlin", "Android SDK", "Biometrics", "Security"]
      play_store_link = "#"

    [[params.portfolio.items]]
      icon = "fas fa-chart-line"
      preview_title = "Business"
      preview_subtitle = "Website"
      title = "Business Portfolio Website"
      description = "Professional website for business portfolio with modern design, fast loading, and excellent SEO optimization."
      technologies = ["Hugo", "HTML/CSS", "JavaScript", "SEO"]
      demo_link = "#"
      github_link = "#"

  # About Section
  [params.about]
    badge = "About HDCode"
    title = "Building Digital Excellence"
    description = "We specialize in building custom software solutions that help small and medium businesses establish their digital presence. Our focus is on creating practical, reliable solutions that grow with your business."
    cta_text = "Learn More About Us"

    # About Stats (using global stats)
    [[params.about.stats]]
      icon = "fas fa-code"
      number_key = "projects_completed"
      label = "Projects Completed"
    [[params.about.stats]]
      icon = "fas fa-users"
      number_key = "happy_clients"
      label = "Happy Clients"
    [[params.about.stats]]
      icon = "fas fa-award"
      number_key = "years_experience"
      label = "Years Experience"

    # Tech Stack
    [[params.about.technologies]]
      icon = "fab fa-android"
      name = "Android"
    [[params.about.technologies]]
      icon = "fas fa-code"
      name = "Kotlin"
    [[params.about.technologies]]
      icon = "fab fa-java"
      name = "Java"
    [[params.about.technologies]]
      icon = "fas fa-mobile"
      name = "Flutter"
    [[params.about.technologies]]
      icon = "fas fa-globe"
      name = "Web"

  # Contact Section
  [params.contact]
    badge = "Get In Touch"
    title = "Ready to Start Your Project?"
    description = "Let's discuss your ideas and create something amazing together"
    availability = "24/7 Support Available"
    form_submit_text = "Send Message"

    # Contact Info Labels
    email_label = "Email Us"
    availability_label = "Availability"

    # Form Placeholders
    name_placeholder = "Your Name"
    email_placeholder = "Your Email"
    message_placeholder = "Project details..."

    # Form Messages
    form_sending_text = "Sending..."
    form_wait_text = "Please wait..."
    form_success_title = "Message Sent Successfully!"
    form_success_message = "Thank you for reaching out! We've received your message and will get back to you within 24 hours."
    form_captcha_error = "Please complete the captcha verification."
    form_network_error = "Network error. Please check your connection and try again."
    form_general_error = "Something went wrong!"

  # Header/Navigation
  [params.navigation]
    # Navigation menu items are defined in [[menu.main]] sections above

  # Footer
  [params.footer]
    # Feature flags
    show_projects = false  # Set to true to show the projects section in footer
    show_github = false    # Set to true to show GitHub link in footer
    show_linkedin = false  # Set to true to show LinkedIn link in footer
    show_twitter = false   # Set to true to show Twitter link in footer

    # Social Media Links
    github_url = "https://github.com/hdcode"
    linkedin_url = "https://linkedin.com/company/hdcode"
    twitter_url = "https://twitter.com/hdcode"

    # Footer Columns
    services_title = "Services"
    company_title = "HDCode"
    projects_title = "Projects"
    contact_title = "Contact"

    # Contact Info
    availability_text = "Available 24/7"
    location_text = "Global Remote"

    # Legal Links
    privacy_text = "Privacy Policy"
    terms_text = "Terms of Service"
    privacy_url = "/privacy"
    terms_url = "/terms"

    # Footer Services Links
    [[params.footer.services]]
      name = "Websites"
      url = "#services"
    [[params.footer.services]]
      name = "Android Development"
      url = "#services"
    [[params.footer.services]]
      name = "MVP Development"
      url = "#services"

    # Footer Company Links
    [[params.footer.company]]
      name = "About"
      url = "#about"
    [[params.footer.company]]
      name = "Contact"
      url = "#contact"

    # Footer Projects Links
    [[params.footer.projects]]
      name = "YouTube Looper"
      url = "https://tubli.to"
      description = "Create playlists and loop multiple YouTube videos with queue management"

  # Blog/List Pages
  [params.blog]
    read_more_text = "Read More"
    no_posts_title = "No posts found"
    no_posts_description = "There are no posts to display at the moment."
    share_text = "Share this post"
