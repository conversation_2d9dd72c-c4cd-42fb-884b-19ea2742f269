// HD Digital Premium Theme JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Performance optimization: Use requestIdleCallback for non-critical tasks
    if ('requestIdleCallback' in window) {
        requestIdleCallback(() => {
            initAdvancedAnimations();
            initMicroInteractions();
        });
    } else {
        setTimeout(() => {
            initAdvancedAnimations();
            initMicroInteractions();
        }, 100);
    }

    // Initialize critical components immediately
    initLoadingScreen();
    initMobileMenu();
    initSmoothScrolling();
    initScrollEffects();
    initAnimations();
    initHDCodeAnimations();
    initStatsCounter();
    initShowcaseInteractions();
    initCountdownTimer();
    initPerformanceOptimizations();
});

// Loading Screen
function initLoadingScreen() {
    const loadingScreen = document.getElementById('loading-screen');
    
    // Hide loading screen after page load
    window.addEventListener('load', function() {
        setTimeout(() => {
            loadingScreen.style.opacity = '0';
            setTimeout(() => {
                loadingScreen.style.display = 'none';
            }, 500);
        }, 1000);
    });
}

// Mobile Menu
function initMobileMenu() {
    const mobileToggle = document.querySelector('.mobile-menu-toggle');
    const mobileMenu = document.querySelector('.mobile-menu');
    const mobileLinks = document.querySelectorAll('.mobile-nav-link');
    
    if (mobileToggle && mobileMenu) {
        mobileToggle.addEventListener('click', function() {
            mobileMenu.classList.toggle('active');
            mobileToggle.classList.toggle('active');
        });
        
        // Close menu when clicking on links
        mobileLinks.forEach(link => {
            link.addEventListener('click', function() {
                mobileMenu.classList.remove('active');
                mobileToggle.classList.remove('active');
            });
        });
        
        // Close menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!mobileToggle.contains(e.target) && !mobileMenu.contains(e.target)) {
                mobileMenu.classList.remove('active');
                mobileToggle.classList.remove('active');
            }
        });
    }
}

// Smooth Scrolling
function initSmoothScrolling() {
    const links = document.querySelectorAll('a[href^="#"]');
    
    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);
            
            if (targetElement) {
                const headerHeight = document.querySelector('.premium-header').offsetHeight;
                const targetPosition = targetElement.offsetTop - headerHeight - 20;
                
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });
}

// Scroll Effects
function initScrollEffects() {
    const header = document.querySelector('.premium-header');
    let lastScrollY = window.scrollY;
    
    window.addEventListener('scroll', function() {
        const currentScrollY = window.scrollY;
        
        // Header background opacity
        if (currentScrollY > 50) {
            header.style.background = 'rgba(0, 0, 0, 0.95)';
        } else {
            header.style.background = 'rgba(0, 0, 0, 0.9)';
        }
        
        // Hide/show header on scroll
        if (currentScrollY > lastScrollY && currentScrollY > 100) {
            header.style.transform = 'translateY(-100%)';
        } else {
            header.style.transform = 'translateY(0)';
        }
        
        lastScrollY = currentScrollY;
    });
}

// Animations
function initAnimations() {
    // Intersection Observer for fade-in animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    const animatedElements = document.querySelectorAll('.service-card, .section-header');
    animatedElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease-out, transform 0.6s ease-out';
        observer.observe(el);
    });
    
    // Parallax effect for hero background
    window.addEventListener('scroll', function() {
        const scrolled = window.pageYOffset;
        const heroBackground = document.querySelector('.hero-background');
        
        if (heroBackground) {
            const speed = scrolled * 0.5;
            heroBackground.style.transform = `translateY(${speed}px)`;
        }
    });
    
    // Premium hero interactions
    initHeroPremiumEffects();
}

// Premium Hero Effects
function initHeroPremiumEffects() {
    const heroSection = document.querySelector('.hero-section');
    const heroVisual = document.querySelector('.hero-visual');

    if (!heroSection || !heroVisual) return;

    // Premium mouse move effect
    heroSection.addEventListener('mousemove', throttle(function(e) {
        const rect = heroSection.getBoundingClientRect();
        const x = (e.clientX - rect.left) / rect.width;
        const y = (e.clientY - rect.top) / rect.height;

        // Subtle parallax effect on hero particles
        const particles = document.querySelector('.hero-particles');
        if (particles) {
            const moveX = (x - 0.5) * 20;
            const moveY = (y - 0.5) * 20;
            particles.style.transform = `translate(${moveX}px, ${moveY}px)`;
        }

        // Interactive glow effect
        const heroBackground = document.querySelector('.hero-background');
        if (heroBackground) {
            heroBackground.style.background = `
                radial-gradient(circle at ${x * 100}% ${y * 100}%, rgba(99, 102, 241, 0.2) 0%, transparent 50%),
                radial-gradient(circle at ${(1-x) * 100}% ${(1-y) * 100}%, rgba(139, 92, 246, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 50% 50%, rgba(6, 182, 212, 0.1) 0%, transparent 70%)
            `;
        }
    }, 50));

    // Reset on mouse leave
    heroSection.addEventListener('mouseleave', function() {
        const particles = document.querySelector('.hero-particles');
        const heroBackground = document.querySelector('.hero-background');

        if (particles) {
            particles.style.transform = 'translate(0, 0)';
        }

        if (heroBackground) {
            heroBackground.style.background = `
                radial-gradient(circle at 25% 25%, rgba(99, 102, 241, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.12) 0%, transparent 50%),
                radial-gradient(circle at 50% 50%, rgba(6, 182, 212, 0.08) 0%, transparent 70%)
            `;
        }
    });

    // Premium button interactions
    const buttons = document.querySelectorAll('.primary-button, .secondary-button');
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px) scale(1.02)';
        });

        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
}

// Utility Functions
function throttle(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Add smooth hover effects
document.addEventListener('mouseover', function(e) {
    if (e.target.matches('.primary-button, .secondary-button, .cta-button')) {
        e.target.style.transform = 'translateY(-2px)';
    }
});

document.addEventListener('mouseout', function(e) {
    if (e.target.matches('.primary-button, .secondary-button, .cta-button')) {
        e.target.style.transform = 'translateY(0)';
    }
});

// Performance optimization
const throttledScroll = throttle(initScrollEffects, 16);
window.addEventListener('scroll', throttledScroll);

// HDCode Clean Animation
function initHDCodeAnimations() {
    const hdcodeElement = document.getElementById('hdcode-text');
    if (!hdcodeElement) return;

    // Add subtle hover effect
    hdcodeElement.addEventListener('mouseenter', function() {
        this.style.transform = 'scale(1.05)';
        this.style.filter = 'brightness(1.2)';
    });

    hdcodeElement.addEventListener('mouseleave', function() {
        this.style.transform = 'scale(1)';
        this.style.filter = 'brightness(1)';
    });
}

// Stats Counter Animation
function initStatsCounter() {
    const statNumbers = document.querySelectorAll('.stat-number, .metric-number');
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const target = entry.target;
                const targetValue = target.getAttribute('data-target');
                animateCounter(target, targetValue);
                observer.unobserve(target);
            }
        });
    }, { threshold: 0.5 });

    statNumbers.forEach(stat => observer.observe(stat));
}

function animateCounter(element, target) {
    const isPercentage = target.includes('%');
    const isPlus = target.includes('+');
    const isDollar = target.includes('$');
    const isSlash = target.includes('/');

    // Extract numeric value, handling special cases like $50M+
    let numericValue;
    if (isDollar && target.includes('M')) {
        numericValue = parseInt(target.replace(/[^\d]/g, ''));
    } else {
        numericValue = parseInt(target.replace(/[^\d]/g, ''));
    }

    let current = 0;
    const increment = numericValue / 50; // 50 steps for smooth animation
    const timer = setInterval(() => {
        current += increment;
        if (current >= numericValue) {
            current = numericValue;
            clearInterval(timer);
        }

        let displayValue = Math.floor(current);

        // Format the display value based on the original target
        if (isDollar && target.includes('M')) {
            displayValue = '$' + displayValue + 'M+';
        } else if (isPercentage) {
            displayValue += '%';
        } else if (isPlus) {
            displayValue += '+';
        } else if (isSlash) {
            displayValue = target.replace(/\d+/, displayValue);
        }

        element.textContent = displayValue;
    }, 40); // 40ms intervals for smooth animation
}

// Showcase Card Interactions
function initShowcaseInteractions() {
    const showcaseCards = document.querySelectorAll('.showcase-card');

    showcaseCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            // Pause the floating animation on hover
            this.style.animationPlayState = 'paused';
        });

        card.addEventListener('mouseleave', function() {
            // Resume the floating animation
            this.style.animationPlayState = 'running';
        });

        // Add click interaction for mobile
        card.addEventListener('click', function() {
            this.style.transform = 'translateY(-5px) scale(1.05)';
            setTimeout(() => {
                this.style.transform = '';
            }, 200);
        });
    });
}

// Countdown Timer for Urgency
function initCountdownTimer() {
    const daysElement = document.getElementById('days');
    const hoursElement = document.getElementById('hours');
    const minutesElement = document.getElementById('minutes');

    if (!daysElement || !hoursElement || !minutesElement) return;

    // Set target date (30 days from now for demo purposes)
    const targetDate = new Date();
    targetDate.setDate(targetDate.getDate() + 30);

    function updateTimer() {
        const now = new Date().getTime();
        const distance = targetDate.getTime() - now;

        if (distance < 0) {
            // Timer expired, reset to 30 days
            targetDate.setDate(new Date().getDate() + 30);
            return;
        }

        const days = Math.floor(distance / (1000 * 60 * 60 * 24));
        const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));

        daysElement.textContent = days.toString().padStart(2, '0');
        hoursElement.textContent = hours.toString().padStart(2, '0');
        minutesElement.textContent = minutes.toString().padStart(2, '0');
    }

    // Update timer immediately and then every minute
    updateTimer();
    setInterval(updateTimer, 60000);
}

// Advanced Animations
function initAdvancedAnimations() {
    // Intersection Observer for reveal animations
    const revealObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('revealed');
                revealObserver.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });

    // Observe elements for reveal animations
    const revealElements = document.querySelectorAll('.reveal-up, .reveal-left, .reveal-right, .reveal-scale');
    revealElements.forEach(el => revealObserver.observe(el));

    // Parallax effect for multiple elements
    window.addEventListener('scroll', throttle(() => {
        const scrolled = window.pageYOffset;
        const parallaxElements = document.querySelectorAll('.parallax-element');

        parallaxElements.forEach((element, index) => {
            const speed = 0.5 + (index * 0.1); // Different speeds for different elements
            const yPos = -(scrolled * speed);
            element.style.transform = `translateY(${yPos}px)`;
        });
    }, 16));

    // Staggered animations for grids
    const staggerContainers = document.querySelectorAll('.services-grid, .testimonials-grid, .trust-badges');
    staggerContainers.forEach(container => {
        const children = container.children;
        Array.from(children).forEach((child, index) => {
            child.style.animationDelay = `${index * 0.1}s`;
            child.classList.add('stagger-animation');
        });
    });
}

// Micro-interactions
function initMicroInteractions() {
    // Magnetic effect for buttons and cards
    const magneticElements = document.querySelectorAll('.service-card, .testimonial-card, .trust-badge, .primary-button, .secondary-button');

    magneticElements.forEach(element => {
        element.addEventListener('mouseenter', function() {
            this.classList.add('magnetic-element');
        });

        element.addEventListener('mousemove', function(e) {
            const rect = this.getBoundingClientRect();
            const x = e.clientX - rect.left - rect.width / 2;
            const y = e.clientY - rect.top - rect.height / 2;

            const moveX = x * 0.1;
            const moveY = y * 0.1;

            this.style.transform = `translate3d(${moveX}px, ${moveY}px, 0) scale(1.02)`;
        });

        element.addEventListener('mouseleave', function() {
            this.style.transform = 'translate3d(0, 0, 0) scale(1)';
            this.classList.remove('magnetic-element');
        });
    });

    // Ripple effect for buttons
    const rippleButtons = document.querySelectorAll('.primary-button, .secondary-button, .method-cta');
    rippleButtons.forEach(button => {
        button.classList.add('ripple-effect');

        button.addEventListener('click', function(e) {
            const rect = this.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            const ripple = document.createElement('span');
            ripple.style.position = 'absolute';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.style.width = '0';
            ripple.style.height = '0';
            ripple.style.borderRadius = '50%';
            ripple.style.background = 'rgba(255, 255, 255, 0.6)';
            ripple.style.transform = 'translate(-50%, -50%)';
            ripple.style.animation = 'ripple-animation 0.6s linear';

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });

    // Enhanced hover effects for cards
    const premiumCards = document.querySelectorAll('.service-card, .testimonial-card, .metric-card');
    premiumCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) rotateX(5deg) rotateY(5deg)';
            this.style.boxShadow = '0 25px 50px rgba(0, 0, 0, 0.3)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) rotateX(0) rotateY(0)';
            this.style.boxShadow = '';
        });
    });

    // Smooth page transitions
    document.body.classList.add('page-transition');
    setTimeout(() => {
        document.body.classList.add('loaded');
    }, 100);

    // Add floating animation to specific elements
    const floatingElements = document.querySelectorAll('.hero-showcase .showcase-card, .urgency-icon');
    floatingElements.forEach(element => {
        element.classList.add('floating-element');
    });

    // Add pulse effect to urgency elements
    const urgencyElements = document.querySelectorAll('.urgency-banner, .timer-number');
    urgencyElements.forEach(element => {
        element.classList.add('pulse-effect');
    });
}

// Performance Optimizations
function initPerformanceOptimizations() {
    // Lazy loading for images
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    if (img.dataset.src) {
                        img.src = img.dataset.src;
                        img.classList.add('loaded');
                        imageObserver.unobserve(img);
                    }
                }
            });
        });

        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    }

    // Preload critical resources
    const criticalResources = [
        '/css/premium.css',
        '/js/premium.js'
    ];

    criticalResources.forEach(resource => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.href = resource;
        link.as = resource.endsWith('.css') ? 'style' : 'script';
        document.head.appendChild(link);
    });

    // Optimize scroll performance
    let ticking = false;
    function updateScrollEffects() {
        // Batch DOM reads and writes
        const scrollY = window.pageYOffset;

        // Update parallax elements
        requestAnimationFrame(() => {
            document.querySelectorAll('.parallax-element').forEach((element, index) => {
                const speed = 0.5 + (index * 0.1);
                const yPos = -(scrollY * speed);
                element.style.transform = `translateY(${yPos}px)`;
            });
        });

        ticking = false;
    }

    window.addEventListener('scroll', () => {
        if (!ticking) {
            requestAnimationFrame(updateScrollEffects);
            ticking = true;
        }
    }, { passive: true });

    // Reduce motion for users who prefer it
    if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
        document.documentElement.style.setProperty('--transition-normal', '0.01ms');
        document.documentElement.style.setProperty('--transition-slow', '0.01ms');

        // Disable animations
        document.querySelectorAll('.floating-element, .pulse-effect, .morphing-shape').forEach(el => {
            el.style.animation = 'none';
        });
    }

    // Touch device optimizations
    if ('ontouchstart' in window) {
        document.body.classList.add('touch-device');

        // Remove hover effects on touch devices
        const style = document.createElement('style');
        style.textContent = `
            .touch-device .service-card:hover,
            .touch-device .testimonial-card:hover,
            .touch-device .metric-card:hover {
                transform: none !important;
                box-shadow: none !important;
            }
        `;
        document.head.appendChild(style);
    }

    // Memory management
    window.addEventListener('beforeunload', () => {
        // Clean up event listeners and observers
        document.querySelectorAll('*').forEach(el => {
            el.onscroll = null;
            el.onresize = null;
            el.onmousemove = null;
        });
    });

    // Progressive enhancement
    document.body.classList.add('js-enabled');

    // Add GPU acceleration to animated elements
    document.querySelectorAll('.service-card, .testimonial-card, .hero-showcase').forEach(el => {
        el.classList.add('gpu-accelerated');
    });
}

// Throttle function for performance
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// Debounce function for performance
function debounce(func, wait, immediate) {
    let timeout;
    return function() {
        const context = this, args = arguments;
        const later = function() {
            timeout = null;
            if (!immediate) func.apply(context, args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func.apply(context, args);
    };
}

// CSS Keyframe animations injection
const style = document.createElement('style');
style.textContent = `
    @keyframes ripple-animation {
        to {
            width: 200px;
            height: 200px;
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);
