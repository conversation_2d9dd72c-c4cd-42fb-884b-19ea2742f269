/* HD Digital Premium Theme */

/* CSS Variables - Premium Color Palette */
:root {
    /* Primary Colors - Warm and inviting */
    --primary-900: #0f172a;
    --primary-800: #1e293b;
    --primary-700: #334155;
    --primary-600: #475569;
    --primary-500: #64748b;

    /* Accent Colors - Vibrant and happy */
    --accent-primary: #3b82f6;    /* Bright blue */
    --accent-secondary: #10b981;  /* Emerald green */
    --accent-tertiary: #f59e0b;   /* Warm amber */
    --accent-quaternary: #ec4899; /* Pink */
    --accent-purple: #8b5cf6;     /* Purple */
    --accent-cyan: #06b6d4;       /* Cyan */
    --accent-orange: #f97316;     /* Orange */

    /* Gradient Colors - More vibrant and energetic */
    --gradient-primary: linear-gradient(135deg, #3b82f6 0%, #10b981 100%);
    --gradient-secondary: linear-gradient(135deg, #10b981 0%, #f59e0b 100%);
    --gradient-accent: linear-gradient(135deg, #f59e0b 0%, #ec4899 100%);
    --gradient-hero: linear-gradient(135deg, #3b82f6 0%, #10b981 50%, #f59e0b 100%);

    /* Text Colors - Improved contrast for better readability */
    --text-primary: #ffffff;
    --text-secondary: #e2e8f0;
    --text-muted: #cbd5e1;
    --text-dark: #1e293b;

    /* Background Colors - Lighter and more welcoming */
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --bg-card: rgba(248, 250, 252, 0.08);
    --bg-glass: rgba(248, 250, 252, 0.12);

    /* Spacing - Optimized for more compact layout */
    --spacing-xs: 0.5rem;
    --spacing-sm: 0.875rem;
    --spacing-md: 1.25rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 2.5rem;
    --spacing-3xl: 3.5rem;

    /* Layout */
    --container-max-width: 1400px;

    /* Typography - Premium System */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;
    --font-mono: 'JetBrains Mono', 'Fira Code', 'Courier New', monospace;

    /* Typography Scale */
    --text-xs: 0.75rem;      /* 12px */
    --text-sm: 0.875rem;     /* 14px */
    --text-base: 1rem;       /* 16px */
    --text-lg: 1.125rem;     /* 18px */
    --text-xl: 1.25rem;      /* 20px */
    --text-2xl: 1.5rem;      /* 24px */
    --text-3xl: 1.875rem;    /* 30px */
    --text-4xl: 2.25rem;     /* 36px */
    --text-5xl: 3rem;        /* 48px */
    --text-6xl: 3.75rem;     /* 60px */
    --text-7xl: 4.5rem;      /* 72px */
    --text-8xl: 6rem;        /* 96px */

    /* Line Heights */
    --leading-none: 1;
    --leading-tight: 1.25;
    --leading-snug: 1.375;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    --leading-loose: 2;

    /* Font Weights */
    --font-thin: 100;
    --font-light: 300;
    --font-normal: 400;
    --font-medium: 500;
    --font-semibold: 600;
    --font-bold: 700;
    --font-extrabold: 800;
    --font-black: 900;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    --shadow-premium: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

    /* Borders */
    --border-radius-sm: 0.375rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
    --border-radius-2xl: 1.5rem;

    /* Transitions */
    --transition-fast: 0.15s ease-out;
    --transition-normal: 0.3s ease-out;
    --transition-slow: 0.5s ease-out;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-primary);
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: var(--leading-normal);
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
    text-rendering: optimizeLegibility;
}

/* Premium Typography System */

/* Headings */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-display);
    font-weight: var(--font-bold);
    line-height: var(--leading-tight);
    letter-spacing: -0.025em;
    margin: 0;
}

h1 {
    font-size: var(--text-6xl);
    font-weight: var(--font-extrabold);
    line-height: var(--leading-none);
    letter-spacing: -0.05em;
}

h2 {
    font-size: var(--text-4xl);
    font-weight: var(--font-bold);
}

h3 {
    font-size: var(--text-2xl);
    font-weight: var(--font-semibold);
}

h4 {
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
}

h5 {
    font-size: var(--text-lg);
    font-weight: var(--font-medium);
}

h6 {
    font-size: var(--text-base);
    font-weight: var(--font-medium);
}

/* Body Text */
p {
    font-size: var(--text-base);
    line-height: var(--leading-relaxed);
    margin: 0 0 var(--spacing-md) 0;
}

.text-large {
    font-size: var(--text-lg);
    line-height: var(--leading-relaxed);
}

.text-small {
    font-size: var(--text-sm);
    line-height: var(--leading-normal);
}

.text-xs {
    font-size: var(--text-xs);
    line-height: var(--leading-normal);
}

/* Display Text */
.display-text {
    font-family: var(--font-display);
    font-size: var(--text-7xl);
    font-weight: var(--font-black);
    line-height: var(--leading-none);
    letter-spacing: -0.05em;
}

.hero-text {
    font-family: var(--font-display);
    font-size: var(--text-6xl);
    font-weight: var(--font-extrabold);
    line-height: var(--leading-none);
    letter-spacing: -0.04em;
}

/* Premium Text Styles */
.text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-gradient-secondary {
    background: var(--gradient-secondary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-gradient-accent {
    background: var(--gradient-accent);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Text Weights */
.font-light { font-weight: var(--font-light); }
.font-normal { font-weight: var(--font-normal); }
.font-medium { font-weight: var(--font-medium); }
.font-semibold { font-weight: var(--font-semibold); }
.font-bold { font-weight: var(--font-bold); }
.font-extrabold { font-weight: var(--font-extrabold); }
.font-black { font-weight: var(--font-black); }

/* Text Colors */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }
.text-accent { color: var(--accent-primary); }
.text-success { color: var(--accent-secondary); }
.text-warning { color: var(--accent-tertiary); }

/* Letter Spacing */
.tracking-tighter { letter-spacing: -0.05em; }
.tracking-tight { letter-spacing: -0.025em; }
.tracking-normal { letter-spacing: 0; }
.tracking-wide { letter-spacing: 0.025em; }
.tracking-wider { letter-spacing: 0.05em; }
.tracking-widest { letter-spacing: 0.1em; }

/* Line Heights */
.leading-none { line-height: var(--leading-none); }
.leading-tight { line-height: var(--leading-tight); }
.leading-snug { line-height: var(--leading-snug); }
.leading-normal { line-height: var(--leading-normal); }
.leading-relaxed { line-height: var(--leading-relaxed); }
.leading-loose { line-height: var(--leading-loose); }

/* Text Alignment */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

/* Text Transform */
.uppercase { text-transform: uppercase; }
.lowercase { text-transform: lowercase; }
.capitalize { text-transform: capitalize; }
.normal-case { text-transform: none; }

/* Premium Text Effects */
.text-shadow-sm {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.text-glow {
    text-shadow: 0 0 10px currentColor;
}

.text-outline {
    -webkit-text-stroke: 1px var(--text-primary);
    text-stroke: 1px var(--text-primary);
}

/* Loading Screen */
#loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease-out;
}

.loading-content {
    text-align: center;
}

.loading-logo {
    font-family: var(--font-display);
    font-size: 2rem;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--spacing-md);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(59, 130, 246, 0.3);
    border-top: 3px solid var(--accent-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Premium Header */
.premium-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: all var(--transition-normal);
}

.premium-nav {
    padding: var(--spacing-sm) 0;
}

.nav-container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.nav-logo .logo-link {
    text-decoration: none;
    display: flex;
    align-items: center;
}

.logo-text {
    font-family: var(--font-display);
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-primary);
    transition: all var(--transition-normal);
}

.logo-accent {
    color: var(--accent-primary);
    font-size: 2rem;
    margin-left: 2px;
}

.nav-logo:hover .logo-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Desktop Navigation */
.desktop-menu {
    display: flex;
    align-items: center;
    gap: var(--spacing-xl);
}

.nav-link {
    position: relative;
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 0.95rem;
    transition: all var(--transition-normal);
    padding: var(--spacing-xs) 0;
}

.nav-link::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: width var(--transition-normal);
}

.nav-link:hover {
    color: var(--text-primary);
}

.nav-link:hover::before {
    width: 100%;
}



/* Mobile Menu */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 2px;
    background: var(--text-primary);
    transition: all var(--transition-normal);
}

.mobile-menu {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-menu-content {
    padding: var(--spacing-lg);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.mobile-nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: color var(--transition-normal);
}

.mobile-nav-link:hover {
    color: var(--text-primary);
}

.mobile-cta-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--gradient-primary);
    color: var(--text-primary);
    text-decoration: none;
    border-radius: var(--border-radius-xl);
    font-weight: 600;
    margin-top: var(--spacing-md);
}

/* Hero Section - Premium Enhanced */
.hero-section {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
    padding-top: 80px;
    background:
        linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.9) 50%, rgba(15, 23, 42, 0.95) 100%),
        radial-gradient(ellipse at top, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
        radial-gradient(ellipse at bottom, rgba(16, 185, 129, 0.08) 0%, transparent 50%);
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
    background:
        radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(16, 185, 129, 0.12) 0%, transparent 50%),
        radial-gradient(circle at 50% 50%, rgba(245, 158, 11, 0.08) 0%, transparent 70%);
}

.hero-grid {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        linear-gradient(rgba(59, 130, 246, 0.08) 1px, transparent 1px),
        linear-gradient(90deg, rgba(59, 130, 246, 0.08) 1px, transparent 1px);
    background-size: 60px 60px;
    animation: gridMove 30s linear infinite;
    opacity: 0.6;
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(60px, 60px); }
}

.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 15% 15%, rgba(59, 130, 246, 0.12) 0%, transparent 40%),
        radial-gradient(circle at 85% 85%, rgba(16, 185, 129, 0.10) 0%, transparent 45%),
        radial-gradient(circle at 45% 65%, rgba(245, 158, 11, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 70% 30%, rgba(236, 72, 153, 0.06) 0%, transparent 35%);
    animation: particleFloat 20s ease-in-out infinite;
}

@keyframes particleFloat {
    0%, 100% { opacity: 0.8; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.05); }
}

/* New Premium Background Elements */
.hero-gradient-orbs {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
}

.hero-gradient-orbs::before,
.hero-gradient-orbs::after {
    content: '';
    position: absolute;
    border-radius: 50%;
    filter: blur(80px);
    animation: orbFloat 15s ease-in-out infinite;
}

.hero-gradient-orbs::before {
    width: 400px;
    height: 400px;
    background: radial-gradient(circle, rgba(59, 130, 246, 0.3) 0%, transparent 70%);
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.hero-gradient-orbs::after {
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(16, 185, 129, 0.25) 0%, transparent 70%);
    bottom: 20%;
    right: 15%;
    animation-delay: 7s;
}

@keyframes orbFloat {
    0%, 100% { transform: translate(0, 0) scale(1); opacity: 0.6; }
    33% { transform: translate(30px, -20px) scale(1.1); opacity: 0.8; }
    66% { transform: translate(-20px, 30px) scale(0.9); opacity: 0.7; }
}

.hero-floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    pointer-events: none;
}

.hero-floating-elements::before,
.hero-floating-elements::after {
    content: '';
    position: absolute;
    width: 2px;
    height: 2px;
    background: rgba(59, 130, 246, 0.6);
    border-radius: 50%;
    box-shadow:
        0 0 10px rgba(59, 130, 246, 0.8),
        0 0 20px rgba(59, 130, 246, 0.4);
    animation: floatingDots 8s linear infinite;
}

.hero-floating-elements::before {
    top: 30%;
    left: 20%;
    animation-delay: 0s;
}

.hero-floating-elements::after {
    top: 60%;
    right: 25%;
    animation-delay: 4s;
    background: rgba(16, 185, 129, 0.6);
    box-shadow:
        0 0 10px rgba(16, 185, 129, 0.8),
        0 0 20px rgba(16, 185, 129, 0.4);
}

@keyframes floatingDots {
    0% { transform: translateY(0) rotate(0deg); opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { transform: translateY(-100px) rotate(360deg); opacity: 0; }
}

.hero-container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: center;
    position: relative;
    z-index: 2;
    min-height: calc(100vh - 120px);
}

.hero-content {
    z-index: 3;
    animation: heroContentFadeIn 1.2s ease-out;
    max-width: 600px;
}

@keyframes heroContentFadeIn {
    0% { opacity: 0; transform: translateY(30px); }
    100% { opacity: 1; transform: translateY(0); }
}

/* Premium Badge Container */
.hero-badge-container {
    margin-bottom: var(--spacing-lg);
    animation: badgeSlideIn 1s ease-out 0.5s both;
}

@keyframes badgeSlideIn {
    0% { opacity: 0; transform: translateY(-20px); }
    100% { opacity: 1; transform: translateY(0); }
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-lg);
    background:
        linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(16, 185, 129, 0.1) 100%),
        rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: var(--border-radius-2xl);
    color: var(--text-primary);
    font-size: 0.875rem;
    font-weight: 600;
    backdrop-filter: blur(20px);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    box-shadow:
        0 8px 32px rgba(59, 130, 246, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.badge-glow {
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(59, 130, 246, 0.3) 0%, transparent 70%);
    animation: badgeGlow 3s ease-in-out infinite;
    z-index: -1;
}

@keyframes badgeGlow {
    0%, 100% { opacity: 0.5; transform: scale(0.8); }
    50% { opacity: 1; transform: scale(1.2); }
}

.badge-shine {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.8s ease;
}

.hero-badge:hover .badge-shine {
    left: 100%;
}

.hero-badge:hover {
    transform: translateY(-2px);
    box-shadow:
        0 12px 40px rgba(59, 130, 246, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Enhanced Hero Title */
.hero-title {
    font-family: var(--font-display);
    font-size: clamp(var(--text-4xl), 6vw, var(--text-7xl));
    font-weight: var(--font-black);
    line-height: var(--leading-none);
    margin-bottom: var(--spacing-xl);
    letter-spacing: var(--tracking-tighter);
    position: relative;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.title-line {
    display: block;
    animation: titleLineSlideIn 0.8s ease-out forwards;
    opacity: 0;
    transform: translateX(-30px);
}

.title-line-1 { animation-delay: 0.2s; }
.title-line-2 { animation-delay: 0.4s; }
.title-line-3 { animation-delay: 0.6s; }

@keyframes titleLineSlideIn {
    to { opacity: 1; transform: translateX(0); }
}

/* Value Proposition Section */
.hero-value-prop {
    margin-bottom: var(--spacing-xl);
    animation: valuePropsSlideIn 1s ease-out 0.8s both;
}

@keyframes valuePropsSlideIn {
    0% { opacity: 0; transform: translateY(20px); }
    100% { opacity: 1; transform: translateY(0); }
}

.hero-description {
    font-size: var(--text-xl);
    line-height: var(--leading-relaxed);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    max-width: 500px;
    font-weight: var(--font-normal);
}

.hero-trust-signals {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.trust-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--text-muted);
    font-size: 0.875rem;
    font-weight: 500;
}

.trust-item i {
    color: var(--accent-secondary);
    font-size: 1rem;
}

/* Enhanced Action Buttons */
.hero-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
    animation: actionsSlideIn 1s ease-out 1s both;
}

@keyframes actionsSlideIn {
    0% { opacity: 0; transform: translateY(20px); }
    100% { opacity: 1; transform: translateY(0); }
}

.primary-action {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.premium-cta {
    position: relative;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg) var(--spacing-2xl);
    background: transparent;
    border: 2px solid transparent;
    border-radius: var(--border-radius-2xl);
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 700;
    font-size: 1.1rem;
    transition: all var(--transition-normal);
    overflow: hidden;
    min-width: 280px;
    justify-content: space-between;
}

.button-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    border-radius: inherit;
    z-index: -2;
}

.button-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 2px;
}

.button-text {
    font-size: 1.1rem;
    font-weight: 700;
}

.button-subtext {
    font-size: 0.875rem;
    font-weight: 500;
    opacity: 0.9;
}

.button-icon {
    font-size: 1.2rem;
    transition: transform var(--transition-normal);
}

.button-shine {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
    z-index: -1;
}

.premium-cta:hover .button-shine {
    left: 100%;
}

.premium-cta:hover .button-icon {
    transform: translateX(5px);
}

.premium-cta:hover {
    transform: translateY(-3px);
    box-shadow:
        0 20px 40px rgba(59, 130, 246, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.1);
}

.cta-urgency {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--accent-tertiary);
    font-size: 0.875rem;
    font-weight: 500;
    animation: urgencyPulse 2s ease-in-out infinite;
}

@keyframes urgencyPulse {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 1; }
}

.secondary-action {
    position: relative;
}

.premium-secondary {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-xl);
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 600;
    transition: all var(--transition-normal);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.button-hover-effect {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.premium-secondary:hover .button-hover-effect {
    opacity: 1;
}

.premium-secondary:hover {
    color: var(--text-primary);
    border-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* Hero Stats */
.hero-stats {
    display: flex;
    gap: var(--spacing-xl);
    animation: statsSlideIn 1s ease-out 1.2s both;
}

@keyframes statsSlideIn {
    0% { opacity: 0; transform: translateY(20px); }
    100% { opacity: 1; transform: translateY(0); }
}

.stat-item {
    position: relative;
    text-align: center;
}

.stat-number {
    font-family: var(--font-display);
    font-size: 2.5rem;
    font-weight: 800;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1;
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-muted);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(59, 130, 246, 0.2) 0%, transparent 70%);
    transform: translate(-50%, -50%);
    z-index: -1;
    animation: statGlow 3s ease-in-out infinite;
}

@keyframes statGlow {
    0%, 100% { opacity: 0.5; transform: translate(-50%, -50%) scale(0.8); }
    50% { opacity: 1; transform: translate(-50%, -50%) scale(1.2); }
}

/* Hero Visual Showcase */
.hero-visual {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    animation: visualSlideIn 1s ease-out 0.6s both;
}

@keyframes visualSlideIn {
    0% { opacity: 0; transform: translateX(30px); }
    100% { opacity: 1; transform: translateX(0); }
}

.hero-showcase {
    position: relative;
    width: 400px;
    height: 400px;
}

.showcase-card {
    position: absolute;
    width: 120px;
    height: 120px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-2xl);
    backdrop-filter: blur(20px);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    transition: all var(--transition-normal);
    cursor: pointer;
}

.showcase-mobile {
    top: 20%;
    left: 10%;
    animation: showcaseFloat 6s ease-in-out infinite;
}

.showcase-web {
    top: 10%;
    right: 20%;
    animation: showcaseFloat 6s ease-in-out infinite 2s;
}

.showcase-mvp {
    bottom: 20%;
    left: 50%;
    transform: translateX(-50%);
    animation: showcaseFloat 6s ease-in-out infinite 4s;
}

@keyframes showcaseFloat {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(2deg); }
}

.card-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
    z-index: 2;
}

.card-content i {
    font-size: 2rem;
    color: var(--accent-primary);
}

.card-content span {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-secondary);
    text-align: center;
}

.card-glow {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle, rgba(59, 130, 246, 0.2) 0%, transparent 70%);
    border-radius: inherit;
    opacity: 0;
    transition: opacity var(--transition-normal);
    z-index: 1;
}

.showcase-card:hover {
    transform: translateY(-5px) scale(1.05);
    border-color: rgba(59, 130, 246, 0.3);
    box-shadow: 0 20px 40px rgba(59, 130, 246, 0.2);
}

.showcase-card:hover .card-glow {
    opacity: 1;
}

/* Gradient Text Effects */
.gradient-text {
    background: var(--gradient-hero);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 4s ease-in-out infinite;
    position: relative;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.gradient-text::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gradient-hero);
    border-radius: 2px;
    opacity: 0.6;
    animation: underlineGlow 2s ease-in-out infinite;
}

@keyframes underlineGlow {
    0%, 100% { opacity: 0.6; transform: scaleX(1); }
    50% { opacity: 1; transform: scaleX(1.05); }
}

/* HDCode Clean Animation */
.hdcode-animated {
    font-weight: 900;
    background: linear-gradient(135deg, #3b82f6 0%, #10b981 100%);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: hdcodeGradientShift 4s ease-in-out infinite;
    transition: all 0.3s ease;
}

@keyframes hdcodeGradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* HDCode Hover Effect */
.hdcode-animated:hover {
    background: linear-gradient(135deg, #60a5fa 0%, #34d399 100%);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Responsive Design */
@media (prefers-reduced-motion: reduce) {
    .hdcode-animated {
        animation: none;
        background: linear-gradient(135deg, #3b82f6 0%, #10b981 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
}

.hero-description {
    font-size: 1.3rem;
    color: var(--text-primary);
    line-height: 1.7;
    margin-bottom: var(--spacing-xl);
    max-width: 580px;
    font-weight: 400;
    opacity: 0;
    animation: descriptionFadeIn 1s ease-out 0.8s forwards;
}

@keyframes descriptionFadeIn {
    to { opacity: 1; }
}

.hero-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    opacity: 0;
    animation: actionsFadeIn 1s ease-out 1s forwards;
}

@keyframes actionsFadeIn {
    to { opacity: 1; }
}

.primary-button {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg) var(--spacing-2xl);
    background: var(--gradient-primary);
    color: var(--text-primary);
    text-decoration: none;
    border: none;
    border-radius: var(--border-radius-2xl);
    font-weight: 700;
    font-size: 1.1rem;
    transition: all var(--transition-normal);
    box-shadow: 0 10px 30px rgba(59, 130, 246, 0.3);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.primary-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.primary-button:hover::before {
    left: 100%;
}

.primary-button:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 20px 40px rgba(59, 130, 246, 0.4);
}

.secondary-button {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg) var(--spacing-2xl);
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
    text-decoration: none;
    border: 2px solid rgba(255, 255, 255, 0.15);
    border-radius: var(--border-radius-2xl);
    font-weight: 600;
    font-size: 1.1rem;
    transition: all var(--transition-normal);
    backdrop-filter: blur(20px);
    position: relative;
    overflow: hidden;
}

.secondary-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.secondary-button:hover::before {
    opacity: 1;
}

.secondary-button:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(59, 130, 246, 0.5);
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}



/* Hero Visual - Premium Enhanced */
.hero-visual {
    position: relative;
    height: 600px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    animation: visualFadeIn 1.5s ease-out 0.5s forwards;
}

@keyframes visualFadeIn {
    to { opacity: 1; }
}

.hero-image-container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: radial-gradient(circle at center, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
    border-radius: var(--border-radius-2xl);
    overflow: hidden;
}

.hero-image-container::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 300px;
    height: 300px;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(16, 185, 129, 0.15) 50%, rgba(245, 158, 11, 0.1) 100%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: heroOrb 8s ease-in-out infinite;
    filter: blur(40px);
}

@keyframes heroOrb {
    0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.8; }
    50% { transform: translate(-50%, -50%) scale(1.2); opacity: 1; }
}

.hero-image-container::after {
    content: '';
    position: absolute;
    top: 20%;
    right: 20%;
    width: 150px;
    height: 150px;
    background: linear-gradient(45deg, rgba(168, 85, 247, 0.15) 0%, rgba(6, 182, 212, 0.1) 100%);
    border-radius: 50%;
    animation: heroOrbSecondary 6s ease-in-out infinite reverse;
    filter: blur(30px);
}

@keyframes heroOrbSecondary {
    0%, 100% { transform: scale(1) rotate(0deg); opacity: 0.6; }
    50% { transform: scale(1.3) rotate(180deg); opacity: 0.9; }
}

/* Premium geometric shapes */
.hero-visual::before {
    content: '';
    position: absolute;
    top: 10%;
    left: 10%;
    width: 80px;
    height: 80px;
    border: 2px solid rgba(59, 130, 246, 0.3);
    border-radius: var(--border-radius-lg);
    animation: geometricFloat 4s ease-in-out infinite;
}

@keyframes geometricFloat {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(45deg); }
}

.hero-visual::after {
    content: '';
    position: absolute;
    bottom: 15%;
    right: 15%;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.2) 0%, rgba(245, 158, 11, 0.15) 100%);
    border-radius: 50%;
    animation: geometricPulse 3s ease-in-out infinite;
}

@keyframes geometricPulse {
    0%, 100% { transform: scale(1); opacity: 0.7; }
    50% { transform: scale(1.2); opacity: 1; }
}

/* Hero Code Preview */
.hero-code-preview {
    position: relative;
    z-index: 10;
    animation: codePreviewSlideIn 1.5s ease-out 1s forwards;
    opacity: 0;
    transform: translateY(30px);
}

@keyframes codePreviewSlideIn {
    to { opacity: 1; transform: translateY(0); }
}

.code-window {
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: var(--border-radius-xl);
    backdrop-filter: blur(20px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    max-width: 350px;
    margin: 0 auto;
}

.code-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.code-controls {
    display: flex;
    gap: var(--spacing-xs);
}

.control {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    opacity: 0.8;
}

.control.red { background: #ef4444; }
.control.yellow { background: #f59e0b; }
.control.green { background: #10b981; }

.code-title {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.code-content {
    padding: var(--spacing-lg);
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    line-height: 1.6;
}

.code-line {
    margin-bottom: var(--spacing-xs);
    animation: codeLineType 0.8s ease-out forwards;
    opacity: 0;
}

.code-line:nth-child(1) { animation-delay: 1.5s; }
.code-line:nth-child(2) { animation-delay: 1.8s; }
.code-line:nth-child(3) { animation-delay: 2.1s; }

@keyframes codeLineType {
    to { opacity: 1; }
}

.code-keyword { color: var(--accent-quaternary); }
.code-variable { color: var(--accent-primary); }
.code-function { color: var(--accent-secondary); }
.code-operator { color: var(--accent-tertiary); }
.code-comment { color: var(--text-muted); font-style: italic; }
/* Hero Responsive Design */
@media (max-width: 1024px) {
    .hero-title {
        font-size: clamp(2.5rem, 8vw, 4rem);
    }
}

@media (max-width: 768px) {
    .hero-section {
        padding-top: 60px;
        min-height: 85vh;
    }

    .hero-container {
        padding: 0 var(--spacing-lg);
        min-height: calc(85vh - 80px);
    }

    .hero-title {
        font-size: clamp(2rem, 10vw, 3.5rem);
        margin-bottom: var(--spacing-md);
    }

    .hero-description {
        font-size: 1.1rem;
        margin-bottom: var(--spacing-lg);
    }

    .hero-actions {
        flex-direction: column;
        gap: var(--spacing-md);
        width: 100%;
        margin-bottom: var(--spacing-lg);
    }

    .primary-button,
    .secondary-button {
        width: 100%;
        justify-content: center;
        padding: var(--spacing-md) var(--spacing-lg);
    }
}

@media (max-width: 480px) {
    .hero-section {
        min-height: 80vh;
    }

    .hero-container {
        padding: 0 var(--spacing-md);
        min-height: calc(80vh - 60px);
    }

    .hero-badge {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: 0.8rem;
        margin-bottom: var(--spacing-sm);
    }

    .hero-title {
        font-size: clamp(1.8rem, 12vw, 3rem);
        line-height: 1.1;
        margin-bottom: var(--spacing-sm);
    }

    .hero-description {
        font-size: 1rem;
        line-height: 1.6;
        margin-bottom: var(--spacing-md);
    }

    .hero-actions {
        margin-bottom: var(--spacing-md);
    }

    .stat-number {
        font-size: 2rem;
    }

    .stat-label {
        font-size: 0.85rem;
    }
}

/* Services Section - Premium Enhanced */
.services-section {
    position: relative;
    padding: var(--spacing-3xl) 0;
    background:
        linear-gradient(180deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-primary) 100%),
        radial-gradient(ellipse at center, rgba(59, 130, 246, 0.05) 0%, transparent 70%);
    overflow: hidden;
}

.services-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
}

.services-gradient-orbs {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.services-gradient-orbs::before,
.services-gradient-orbs::after {
    content: '';
    position: absolute;
    border-radius: 50%;
    filter: blur(100px);
    animation: servicesOrbFloat 20s ease-in-out infinite;
}

.services-gradient-orbs::before {
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(16, 185, 129, 0.2) 0%, transparent 70%);
    top: 10%;
    right: 10%;
    animation-delay: 0s;
}

.services-gradient-orbs::after {
    width: 400px;
    height: 400px;
    background: radial-gradient(circle, rgba(59, 130, 246, 0.15) 0%, transparent 70%);
    bottom: 10%;
    left: 5%;
    animation-delay: 10s;
}

@keyframes servicesOrbFloat {
    0%, 100% { transform: translate(0, 0) scale(1); opacity: 0.6; }
    33% { transform: translate(50px, -30px) scale(1.1); opacity: 0.8; }
    66% { transform: translate(-30px, 40px) scale(0.9); opacity: 0.7; }
}

.services-grid-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        linear-gradient(rgba(16, 185, 129, 0.05) 1px, transparent 1px),
        linear-gradient(90deg, rgba(16, 185, 129, 0.05) 1px, transparent 1px);
    background-size: 80px 80px;
    animation: gridPatternMove 40s linear infinite;
    opacity: 0.4;
}

@keyframes gridPatternMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(80px, 80px); }
}

.section-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    position: relative;
    z-index: 2;
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-3xl);
    animation: sectionHeaderSlideIn 1s ease-out;
}

@keyframes sectionHeaderSlideIn {
    0% { opacity: 0; transform: translateY(30px); }
    100% { opacity: 1; transform: translateY(0); }
}

.premium-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-lg);
    background:
        linear-gradient(135deg, rgba(16, 185, 129, 0.15) 0%, rgba(59, 130, 246, 0.1) 100%),
        rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(16, 185, 129, 0.3);
    border-radius: var(--border-radius-2xl);
    color: var(--accent-secondary);
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
    backdrop-filter: blur(20px);
    box-shadow:
        0 8px 32px rgba(16, 185, 129, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all var(--transition-normal);
}

.premium-badge:hover {
    transform: translateY(-2px);
    box-shadow:
        0 12px 40px rgba(16, 185, 129, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.section-title {
    font-family: var(--font-display);
    font-size: clamp(var(--text-3xl), 5vw, var(--text-5xl));
    font-weight: var(--font-extrabold);
    margin-bottom: var(--spacing-lg);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: var(--leading-tight);
    letter-spacing: var(--tracking-tight);
}

.section-description {
    font-size: var(--text-lg);
    color: var(--text-secondary);
    max-width: 700px;
    margin: 0 auto;
    line-height: var(--leading-relaxed);
    font-weight: var(--font-normal);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
    gap: var(--spacing-2xl);
    margin-top: var(--spacing-2xl);
}

.service-card {
    position: relative;
    background: rgba(255, 255, 255, 0.02);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-2xl);
    padding: var(--spacing-2xl);
    transition: all var(--transition-slow);
    overflow: hidden;
    animation: serviceCardSlideIn 0.8s ease-out forwards;
    opacity: 0;
    transform: translateY(30px);
}

.service-card[data-index="0"] { animation-delay: 0.2s; }
.service-card[data-index="1"] { animation-delay: 0.4s; }
.service-card[data-index="2"] { animation-delay: 0.6s; }

@keyframes serviceCardSlideIn {
    to { opacity: 1; transform: translateY(0); }
}

.card-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
    border-radius: inherit;
    z-index: -2;
}

.card-glow-effect {
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
    opacity: 0;
    transition: opacity var(--transition-slow);
    z-index: -1;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform var(--transition-slow);
    border-radius: var(--border-radius-2xl) var(--border-radius-2xl) 0 0;
}

.service-card:hover::before {
    transform: scaleX(1);
}

.service-card:hover {
    transform: translateY(-15px) scale(1.02);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow:
        0 30px 60px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.service-card:hover .card-glow-effect {
    opacity: 1;
}

.service-card.featured {
    background:
        linear-gradient(135deg, rgba(59, 130, 246, 0.08) 0%, rgba(16, 185, 129, 0.06) 100%),
        rgba(255, 255, 255, 0.03);
    border-color: rgba(59, 130, 246, 0.3);
    box-shadow:
        0 20px 40px rgba(59, 130, 246, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.service-card.featured:hover {
    box-shadow:
        0 35px 70px rgba(59, 130, 246, 0.2),
        0 0 0 1px rgba(59, 130, 246, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Premium Service Badge */
.premium-service-badge {
    position: absolute;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--gradient-accent);
    color: var(--text-primary);
    font-size: 0.75rem;
    font-weight: 700;
    border-radius: var(--border-radius-xl);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);
    animation: badgePulse 2s ease-in-out infinite;
    z-index: 3;
}

@keyframes badgePulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.badge-sparkle {
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    animation: sparkle 1.5s ease-in-out infinite;
}

@keyframes sparkle {
    0%, 100% { opacity: 0; transform: scale(0); }
    50% { opacity: 1; transform: scale(1); }
}

/* Service Header */
.service-header {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.service-icon-container {
    position: relative;
    flex-shrink: 0;
}

.icon-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: var(--border-radius-xl);
    z-index: -2;
}

.service-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow:
        0 10px 30px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all var(--transition-normal);
    z-index: 2;
}

.service-icon i {
    font-size: 2rem;
    color: var(--text-primary);
    transition: all var(--transition-normal);
}

.icon-glow {
    position: absolute;
    top: 0;
    left: 0;
    width: 80px;
    height: 80px;
    background: radial-gradient(circle, rgba(59, 130, 246, 0.4) 0%, transparent 70%);
    border-radius: inherit;
    opacity: 0;
    transition: opacity var(--transition-normal);
    z-index: -1;
}

.service-card:hover .service-icon {
    transform: translateY(-5px) rotate(5deg);
}

.service-card:hover .icon-glow {
    opacity: 1;
}

.service-meta {
    flex: 1;
}

.service-title {
    font-family: var(--font-display);
    font-size: 1.75rem;
    font-weight: 800;
    margin-bottom: var(--spacing-xs);
    color: var(--text-primary);
    line-height: 1.2;
}

.service-popularity {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-xs);
}

.popularity-stars {
    display: flex;
    gap: 2px;
}

.popularity-stars i {
    color: var(--accent-tertiary);
    font-size: 0.875rem;
}

.popularity-text {
    font-size: 0.75rem;
    color: var(--accent-tertiary);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Service Content */
.service-content {
    margin-bottom: var(--spacing-xl);
}

.service-description {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
    font-size: 1.1rem;
}

.service-features {
    margin-bottom: var(--spacing-lg);
}

.features-title {
    font-size: 1rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.features-list {
    list-style: none;
    display: grid;
    gap: var(--spacing-sm);
}

.features-list li {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-secondary);
    font-size: 0.95rem;
    font-weight: 500;
}

.features-list i {
    color: var(--accent-secondary);
    font-size: 1rem;
    flex-shrink: 0;
}

.service-value {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: var(--border-radius-xl);
    margin-bottom: var(--spacing-lg);
}

.value-highlight {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--accent-primary);
    font-weight: 600;
    font-size: 0.95rem;
}

.value-highlight i {
    color: var(--accent-tertiary);
}

.service-timeline {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-muted);
    font-size: 0.875rem;
    font-weight: 500;
}

.service-timeline i {
    color: var(--accent-secondary);
}

/* Service Action */
.service-action {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.premium-service-cta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg) var(--spacing-xl);
    background: var(--gradient-primary);
    border: none;
    border-radius: var(--border-radius-xl);
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 700;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.cta-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 2px;
}

.cta-text {
    font-size: 1.1rem;
    font-weight: 700;
}

.cta-subtext {
    font-size: 0.875rem;
    font-weight: 500;
    opacity: 0.9;
}

.cta-icon {
    font-size: 1.2rem;
    transition: transform var(--transition-normal);
}

.cta-shine {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.premium-service-cta:hover .cta-shine {
    left: 100%;
}

.premium-service-cta:hover .cta-icon {
    transform: translateX(5px);
}

.premium-service-cta:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(59, 130, 246, 0.4);
}

.service-guarantee {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    color: var(--text-muted);
    font-size: 0.875rem;
    font-weight: 500;
}

.service-guarantee i {
    color: var(--accent-secondary);
}

/* Services CTA Section */
.services-cta {
    margin-top: var(--spacing-3xl);
    padding: var(--spacing-2xl);
    background:
        linear-gradient(135deg, rgba(59, 130, 246, 0.08) 0%, rgba(16, 185, 129, 0.06) 100%),
        rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-2xl);
    text-align: center;
    backdrop-filter: blur(20px);
}

.services-cta .cta-content h3 {
    font-family: var(--font-display);
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.services-cta .cta-content p {
    color: var(--text-secondary);
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: var(--spacing-xl);
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.services-cta-button {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg) var(--spacing-2xl);
    background: var(--gradient-secondary);
    border: none;
    border-radius: var(--border-radius-xl);
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 700;
    font-size: 1.1rem;
    transition: all var(--transition-normal);
    box-shadow: 0 10px 30px rgba(16, 185, 129, 0.3);
}

.services-cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(16, 185, 129, 0.4);
}

/* Testimonials Section */
.testimonials-section {
    position: relative;
    padding: var(--spacing-3xl) 0;
    background:
        linear-gradient(180deg, var(--bg-secondary) 0%, var(--bg-primary) 50%, var(--bg-secondary) 100%),
        radial-gradient(ellipse at center, rgba(16, 185, 129, 0.05) 0%, transparent 70%);
    overflow: hidden;
}

.testimonials-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
}

.testimonials-gradient-orbs {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.testimonials-gradient-orbs::before,
.testimonials-gradient-orbs::after {
    content: '';
    position: absolute;
    border-radius: 50%;
    filter: blur(120px);
    animation: testimonialsOrbFloat 25s ease-in-out infinite;
}

.testimonials-gradient-orbs::before {
    width: 400px;
    height: 400px;
    background: radial-gradient(circle, rgba(245, 158, 11, 0.15) 0%, transparent 70%);
    top: 20%;
    left: 5%;
    animation-delay: 0s;
}

.testimonials-gradient-orbs::after {
    width: 350px;
    height: 350px;
    background: radial-gradient(circle, rgba(236, 72, 153, 0.12) 0%, transparent 70%);
    bottom: 20%;
    right: 10%;
    animation-delay: 12s;
}

@keyframes testimonialsOrbFloat {
    0%, 100% { transform: translate(0, 0) scale(1); opacity: 0.6; }
    33% { transform: translate(40px, -50px) scale(1.1); opacity: 0.8; }
    66% { transform: translate(-50px, 30px) scale(0.9); opacity: 0.7; }
}

.testimonials-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(245, 158, 11, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(236, 72, 153, 0.03) 0%, transparent 50%);
    animation: patternShift 30s ease-in-out infinite;
}

@keyframes patternShift {
    0%, 100% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.1) rotate(180deg); }
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-2xl);
    margin-top: var(--spacing-2xl);
}

.testimonial-card {
    position: relative;
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-2xl);
    padding: var(--spacing-2xl);
    transition: all var(--transition-slow);
    overflow: hidden;
    animation: testimonialSlideIn 0.8s ease-out forwards;
    opacity: 0;
    transform: translateY(30px);
}

.testimonial-card[data-index="0"] { animation-delay: 0.2s; }
.testimonial-card[data-index="1"] { animation-delay: 0.4s; }
.testimonial-card[data-index="2"] { animation-delay: 0.6s; }

@keyframes testimonialSlideIn {
    to { opacity: 1; transform: translateY(0); }
}

.testimonial-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
    border-radius: inherit;
    z-index: -2;
}

.testimonial-glow {
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(245, 158, 11, 0.1) 0%, transparent 70%);
    opacity: 0;
    transition: opacity var(--transition-slow);
    z-index: -1;
}

.testimonial-card:hover {
    transform: translateY(-10px) scale(1.02);
    border-color: rgba(245, 158, 11, 0.3);
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(245, 158, 11, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.testimonial-card:hover .testimonial-glow {
    opacity: 1;
}

.testimonial-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
}

.testimonial-quote {
    width: 50px;
    height: 50px;
    background: var(--gradient-accent);
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 10px 30px rgba(245, 158, 11, 0.3);
}

.testimonial-quote i {
    font-size: 1.5rem;
    color: var(--text-primary);
}

.testimonial-rating {
    display: flex;
    gap: 4px;
}

.testimonial-rating i {
    color: var(--accent-tertiary);
    font-size: 1.2rem;
    animation: starTwinkle 2s ease-in-out infinite;
}

.testimonial-rating i:nth-child(1) { animation-delay: 0s; }
.testimonial-rating i:nth-child(2) { animation-delay: 0.2s; }
.testimonial-rating i:nth-child(3) { animation-delay: 0.4s; }
.testimonial-rating i:nth-child(4) { animation-delay: 0.6s; }
.testimonial-rating i:nth-child(5) { animation-delay: 0.8s; }

@keyframes starTwinkle {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(1.1); }
}

.testimonial-content {
    margin-bottom: var(--spacing-xl);
}

.testimonial-text {
    font-size: 1.1rem;
    line-height: 1.7;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    font-style: italic;
}

.testimonial-result {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg);
    background: rgba(245, 158, 11, 0.1);
    border: 1px solid rgba(245, 158, 11, 0.2);
    border-radius: var(--border-radius-xl);
}

.result-highlight {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--accent-tertiary);
    font-weight: 700;
    font-size: 1.1rem;
}

.result-highlight i {
    color: var(--accent-secondary);
}

.project-type {
    color: var(--text-muted);
    font-size: 0.9rem;
    font-weight: 500;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.author-avatar {
    position: relative;
    flex-shrink: 0;
}

.avatar-placeholder {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 10px 30px rgba(59, 130, 246, 0.3);
}

.avatar-placeholder i {
    font-size: 1.5rem;
    color: var(--text-primary);
}

.avatar-glow {
    position: absolute;
    top: 0;
    left: 0;
    width: 60px;
    height: 60px;
    background: radial-gradient(circle, rgba(59, 130, 246, 0.4) 0%, transparent 70%);
    border-radius: 50%;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.testimonial-card:hover .avatar-glow {
    opacity: 1;
}

.author-info {
    flex: 1;
}

.author-name {
    font-family: var(--font-display);
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.author-position {
    color: var(--accent-primary);
    font-weight: 600;
    margin-bottom: 2px;
}

.author-company {
    color: var(--text-muted);
    font-size: 0.9rem;
}

/* Trust Signals Section */
.trust-signals-section {
    padding: var(--spacing-3xl) 0;
    background:
        linear-gradient(180deg, var(--bg-secondary) 0%, var(--bg-primary) 100%),
        radial-gradient(ellipse at center, rgba(59, 130, 246, 0.05) 0%, transparent 70%);
}

.trust-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin: var(--spacing-2xl) 0;
}

.metric-card {
    position: relative;
    text-align: center;
    padding: var(--spacing-xl);
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-2xl);
    transition: all var(--transition-normal);
    animation: metricSlideIn 0.8s ease-out forwards;
    opacity: 0;
    transform: translateY(20px);
}

.metric-card[data-index="0"] { animation-delay: 0.1s; }
.metric-card[data-index="1"] { animation-delay: 0.2s; }
.metric-card[data-index="2"] { animation-delay: 0.3s; }
.metric-card[data-index="3"] { animation-delay: 0.4s; }

@keyframes metricSlideIn {
    to { opacity: 1; transform: translateY(0); }
}

.metric-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
    box-shadow: 0 15px 35px rgba(59, 130, 246, 0.3);
}

.metric-icon i {
    font-size: 2rem;
    color: var(--text-primary);
}

.metric-number {
    font-family: var(--font-display);
    font-size: 3rem;
    font-weight: 800;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1;
    margin-bottom: var(--spacing-sm);
}

.metric-label {
    color: var(--text-secondary);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.9rem;
}

.metric-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, rgba(59, 130, 246, 0.2) 0%, transparent 70%);
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: opacity var(--transition-normal);
    z-index: -1;
}

.metric-card:hover {
    transform: translateY(-10px);
    border-color: rgba(59, 130, 246, 0.3);
    box-shadow: 0 20px 40px rgba(59, 130, 246, 0.2);
}

.metric-card:hover .metric-glow {
    opacity: 1;
}

.trust-badges {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-2xl);
}

.trust-badge {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-xl);
    transition: all var(--transition-normal);
    animation: badgeSlideIn 0.6s ease-out forwards;
    opacity: 0;
    transform: translateX(-20px);
}

.trust-badge[data-index="0"] { animation-delay: 0.1s; }
.trust-badge[data-index="1"] { animation-delay: 0.2s; }
.trust-badge[data-index="2"] { animation-delay: 0.3s; }
.trust-badge[data-index="3"] { animation-delay: 0.4s; }

@keyframes badgeSlideIn {
    to { opacity: 1; transform: translateX(0); }
}

.badge-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-secondary);
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.badge-icon i {
    font-size: 1.5rem;
    color: var(--text-primary);
}

.badge-content {
    flex: 1;
}

.badge-name {
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.badge-description {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.4;
}

.badge-verification {
    color: var(--accent-secondary);
    font-size: 1.2rem;
    flex-shrink: 0;
}

.trust-badge:hover {
    transform: translateY(-5px);
    border-color: rgba(16, 185, 129, 0.3);
    box-shadow: 0 15px 30px rgba(16, 185, 129, 0.2);
}

/* Responsive Design */
@media (max-width: 1200px) {
    /* No hero stats specific styles needed */
}

@media (max-width: 768px) {
    .desktop-menu,
    .nav-cta {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .mobile-menu.active {
        display: block;
    }

    .services-section {
        padding: var(--spacing-xl) 0;
    }

    .section-header {
        margin-bottom: var(--spacing-xl);
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
        margin-top: var(--spacing-lg);
    }

    .service-card {
        padding: var(--spacing-md);
    }
}

/* Experience Domains Section - Premium Enhanced */
.experience-section {
    position: relative;
    padding: var(--spacing-2xl) 0;
    background: linear-gradient(180deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    overflow: hidden;
}

.experience-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 0;
}

.experience-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(16, 185, 129, 0.06) 0%, transparent 50%),
        radial-gradient(circle at 40% 60%, rgba(245, 158, 11, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 70% 30%, rgba(236, 72, 153, 0.04) 0%, transparent 50%);
    animation: experienceParticleFloat 25s ease-in-out infinite;
}

@keyframes experienceParticleFloat {
    0%, 100% { opacity: 0.6; transform: scale(1) rotate(0deg); }
    33% { opacity: 0.8; transform: scale(1.05) rotate(120deg); }
    66% { opacity: 0.7; transform: scale(0.95) rotate(240deg); }
}

.experience-orbs {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.experience-orbs::before {
    content: '';
    position: absolute;
    top: 15%;
    left: 10%;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    animation: experienceOrbFloat 20s ease-in-out infinite;
    filter: blur(40px);
}

.experience-orbs::after {
    content: '';
    position: absolute;
    bottom: 20%;
    right: 15%;
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, rgba(16, 185, 129, 0.08) 0%, transparent 70%);
    border-radius: 50%;
    animation: experienceOrbFloat 15s ease-in-out infinite reverse;
    filter: blur(30px);
}

@keyframes experienceOrbFloat {
    0%, 100% { transform: translate(0, 0) scale(1); opacity: 0.6; }
    50% { transform: translate(30px, -20px) scale(1.2); opacity: 0.8; }
}

.experience-grid {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.experience-card {
    position: relative;
    background: var(--bg-glass);
    backdrop-filter: blur(25px);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: var(--border-radius-2xl);
    padding: 0;
    transition: all var(--transition-slow);
    overflow: hidden;
    opacity: 0;
    transform: translateY(30px);
    animation: experienceCardFadeIn 0.8s ease-out forwards;
}

.experience-card[data-index="0"] { animation-delay: 0.1s; }
.experience-card[data-index="1"] { animation-delay: 0.2s; }
.experience-card[data-index="2"] { animation-delay: 0.3s; }
.experience-card[data-index="3"] { animation-delay: 0.4s; }
.experience-card[data-index="4"] { animation-delay: 0.5s; }
.experience-card[data-index="5"] { animation-delay: 0.6s; }
.experience-card[data-index="6"] { animation-delay: 0.7s; }
.experience-card[data-index="7"] { animation-delay: 0.8s; }
.experience-card[data-index="8"] { animation-delay: 0.9s; }

@keyframes experienceCardFadeIn {
    to { opacity: 1; transform: translateY(0); }
}

.experience-card-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.02) 0%, rgba(255, 255, 255, 0.01) 100%);
    transition: all var(--transition-slow);
}

.experience-card:hover .experience-card-background {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
}

.experience-card-content {
    position: relative;
    z-index: 2;
    padding: var(--spacing-lg);
    text-align: center;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 180px;
}

.experience-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform var(--transition-slow);
    z-index: 3;
}

.experience-card:hover::before {
    transform: scaleX(1);
}

.experience-card:hover {
    transform: translateY(-15px) scale(1.02);
    border-color: rgba(255, 255, 255, 0.15);
    box-shadow:
        var(--shadow-premium),
        0 0 40px rgba(59, 130, 246, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.experience-icon-container {
    position: relative;
    margin-bottom: var(--spacing-md);
    width: 70px;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.experience-icon-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 50%;
    transition: all var(--transition-slow);
    animation: experienceIconPulse 4s ease-in-out infinite;
}

@keyframes experienceIconPulse {
    0%, 100% { transform: scale(1); opacity: 0.6; }
    50% { transform: scale(1.1); opacity: 0.8; }
}

.experience-card:hover .experience-icon-bg {
    transform: scale(1.2);
    opacity: 1;
}

.experience-icon {
    position: relative;
    z-index: 2;
    font-size: 2.5rem;
    transition: all var(--transition-slow);
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

.experience-card:hover .experience-icon {
    transform: scale(1.1) rotate(5deg);
    filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.4));
}

.experience-name {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    line-height: 1.4;
    margin: 0;
    text-align: center;
    transition: all var(--transition-normal);
    position: relative;
    z-index: 2;
}

.experience-card:hover .experience-name {
    color: var(--text-primary);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.experience-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 150px;
    height: 150px;
    transform: translate(-50%, -50%);
    border-radius: 50%;
    opacity: 0;
    transition: opacity var(--transition-slow);
    z-index: 1;
    filter: blur(20px);
}

.experience-card:hover .experience-glow {
    opacity: 1;
}

/* Responsive Design for Experience Section */
@media (max-width: 1024px) {
    .experience-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: var(--spacing-lg);
    }
}

@media (max-width: 768px) {
    .experience-section {
        padding: var(--spacing-xl) 0;
    }

    .experience-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
        margin-top: var(--spacing-lg);
    }

    .experience-card-content {
        padding: var(--spacing-md);
        min-height: 160px;
    }

    .experience-icon-container {
        width: 60px;
        height: 60px;
        margin-bottom: var(--spacing-sm);
    }

    .experience-icon {
        font-size: 1.8rem;
    }

    .experience-name {
        font-size: 0.95rem;
        line-height: 1.3;
    }

    .experience-card:hover {
        transform: translateY(-8px) scale(1.01);
    }
}

@media (max-width: 480px) {
    .services-section,
    .experience-section,
    .about-section,
    .contact-section {
        padding: var(--spacing-lg) 0;
    }

    .section-header {
        margin-bottom: var(--spacing-lg);
    }

    .services-grid,
    .experience-grid {
        margin-top: var(--spacing-md);
        gap: var(--spacing-sm);
    }

    .experience-grid {
        grid-template-columns: 1fr;
    }

    .experience-card-content {
        padding: var(--spacing-sm);
        min-height: 140px;
    }

    .experience-icon-container {
        width: 50px;
        height: 50px;
        margin-bottom: var(--spacing-xs);
    }

    .experience-icon {
        font-size: 1.5rem;
    }

    .experience-name {
        font-size: 0.9rem;
        line-height: 1.2;
    }

    .service-card {
        padding: var(--spacing-sm);
    }

    .service-icon {
        width: 50px;
        height: 50px;
        margin-bottom: var(--spacing-sm);
    }

    .service-title {
        font-size: 1.25rem;
        margin-bottom: var(--spacing-xs);
    }

    .service-description {
        font-size: 0.9rem;
        margin-bottom: var(--spacing-sm);
    }

    .service-features {
        margin-bottom: var(--spacing-sm);
    }
}

/* Portfolio Section */
.portfolio-section {
    padding: var(--spacing-2xl) 0;
    background: var(--bg-secondary);
}

.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.portfolio-item {
    position: relative;
    height: 300px;
    border-radius: var(--border-radius-2xl);
    overflow: hidden;
    background: var(--bg-glass);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all var(--transition-normal);
}

.portfolio-item:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-premium);
}

.portfolio-item:hover .portfolio-image {
    background: linear-gradient(135deg, var(--accent-secondary) 0%, var(--accent-tertiary) 100%);
}

.portfolio-image {
    position: relative;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.portfolio-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    color: var(--text-primary);
    text-align: center;
    z-index: 1;
    transition: all var(--transition-normal);
}

.portfolio-preview i {
    font-size: 3rem;
    opacity: 0.9;
}

.preview-text h4 {
    font-family: var(--font-display);
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
    line-height: 1;
}

.preview-text span {
    font-size: 1rem;
    opacity: 0.8;
    font-weight: 500;
}

.portfolio-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(10px);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: var(--spacing-md);
    opacity: 0;
    transform: translateY(20px);
    transition: all var(--transition-normal);
    z-index: 2;
}

.portfolio-item:hover .portfolio-overlay {
    opacity: 1;
    transform: translateY(0);
}

.portfolio-item:hover .portfolio-preview {
    opacity: 0;
    transform: scale(0.9);
}

.portfolio-info h3 {
    font-family: var(--font-display);
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.portfolio-info p {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    line-height: 1.5;
    font-size: 0.95rem;
}

.portfolio-tech {
    display: flex;
    gap: var(--spacing-xs);
    flex-wrap: wrap;
}

.portfolio-tech span {
    padding: var(--spacing-xs) var(--spacing-sm);
    background: rgba(59, 130, 246, 0.2);
    border: 1px solid rgba(59, 130, 246, 0.4);
    border-radius: var(--border-radius-md);
    font-size: 0.75rem;
    color: var(--accent-primary);
}

.portfolio-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.portfolio-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--bg-glass);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius-lg);
    color: var(--text-primary);
    text-decoration: none;
    transition: all var(--transition-normal);
}

.portfolio-link:hover {
    background: var(--gradient-primary);
    transform: scale(1.1);
}

.portfolio-cta {
    text-align: center;
}

/* About Section */
.about-section {
    padding: var(--spacing-2xl) 0;
    background: linear-gradient(180deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
    align-items: center;
}

.about-description {
    font-size: 1.125rem;
    color: var(--text-primary);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
}

.about-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.stat-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    background: var(--bg-glass);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-lg);
    backdrop-filter: blur(10px);
    transition: all var(--transition-normal);
}

.stat-card:hover {
    transform: translateY(-5px);
    border-color: rgba(255, 255, 255, 0.2);
}

.stat-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-primary);
    font-size: 1.25rem;
}

.stat-number {
    font-family: var(--font-display);
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1;
}

.stat-label {
    color: var(--text-muted);
    font-size: 0.875rem;
}

.tech-stack {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
}

.tech-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg);
    background: var(--bg-glass);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-lg);
    backdrop-filter: blur(10px);
    transition: all var(--transition-normal);
    text-align: center;
}

.tech-item:hover {
    transform: translateY(-5px);
    border-color: var(--accent-primary);
}

.tech-item i {
    font-size: 2rem;
    color: var(--accent-primary);
}

.tech-item span {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 0.875rem;
}
/* Contact Section - Premium Enhanced */
.contact-section {
    position: relative;
    padding: var(--spacing-3xl) 0;
    background:
        linear-gradient(180deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-primary) 100%),
        radial-gradient(ellipse at center, rgba(59, 130, 246, 0.08) 0%, transparent 70%);
    overflow: hidden;
}

.contact-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
}

.contact-gradient-orbs {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.contact-gradient-orbs::before,
.contact-gradient-orbs::after {
    content: '';
    position: absolute;
    border-radius: 50%;
    filter: blur(100px);
    animation: contactOrbFloat 20s ease-in-out infinite;
}

.contact-gradient-orbs::before {
    width: 350px;
    height: 350px;
    background: radial-gradient(circle, rgba(59, 130, 246, 0.2) 0%, transparent 70%);
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.contact-gradient-orbs::after {
    width: 400px;
    height: 400px;
    background: radial-gradient(circle, rgba(16, 185, 129, 0.15) 0%, transparent 70%);
    bottom: 10%;
    right: 5%;
    animation-delay: 10s;
}

@keyframes contactOrbFloat {
    0%, 100% { transform: translate(0, 0) scale(1); opacity: 0.6; }
    33% { transform: translate(30px, -40px) scale(1.1); opacity: 0.8; }
    66% { transform: translate(-40px, 20px) scale(0.9); opacity: 0.7; }
}

.contact-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 30%, rgba(59, 130, 246, 0.05) 0%, transparent 40%),
        radial-gradient(circle at 80% 70%, rgba(16, 185, 129, 0.05) 0%, transparent 40%),
        radial-gradient(circle at 50% 20%, rgba(245, 158, 11, 0.03) 0%, transparent 50%);
    animation: particleShift 25s ease-in-out infinite;
}

@keyframes particleShift {
    0%, 100% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.1) rotate(180deg); }
}

/* Urgency Banner */
.urgency-banner {
    margin: var(--spacing-xl) 0 var(--spacing-2xl);
    padding: var(--spacing-xl);
    background:
        linear-gradient(135deg, rgba(245, 158, 11, 0.15) 0%, rgba(236, 72, 153, 0.1) 100%),
        rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(245, 158, 11, 0.3);
    border-radius: var(--border-radius-2xl);
    backdrop-filter: blur(20px);
    animation: urgencyPulse 3s ease-in-out infinite;
}

@keyframes urgencyPulse {
    0%, 100% { box-shadow: 0 0 20px rgba(245, 158, 11, 0.2); }
    50% { box-shadow: 0 0 30px rgba(245, 158, 11, 0.4); }
}

.urgency-content {
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: var(--spacing-lg);
    align-items: center;
}

.urgency-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-accent);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fireFlicker 1.5s ease-in-out infinite;
}

@keyframes fireFlicker {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.urgency-icon i {
    font-size: 1.5rem;
    color: var(--text-primary);
}

.urgency-text h4 {
    font-family: var(--font-display);
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--accent-tertiary);
    margin-bottom: var(--spacing-xs);
}

.urgency-text p {
    color: var(--text-secondary);
    font-size: 1.1rem;
    margin: 0;
}

.urgency-timer {
    text-align: center;
}

.timer-label {
    display: block;
    color: var(--text-muted);
    font-size: 0.875rem;
    margin-bottom: var(--spacing-sm);
}

.timer-display {
    display: flex;
    gap: var(--spacing-sm);
}

.timer-unit {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: var(--spacing-sm);
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-lg);
    min-width: 60px;
}

.timer-number {
    font-family: var(--font-display);
    font-size: 1.5rem;
    font-weight: 800;
    color: var(--accent-tertiary);
    line-height: 1;
}

.timer-text {
    font-size: 0.75rem;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Contact Content */
.contact-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3xl);
    margin-top: var(--spacing-xl);
}

.contact-options {
    text-align: center;
}

.options-title {
    font-family: var(--font-display);
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xl);
}

.contact-methods {
    display: grid;
    gap: var(--spacing-2xl);
}

/* Primary Contact Method */
.primary-method {
    padding: var(--spacing-2xl);
    background:
        linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(16, 185, 129, 0.08) 100%),
        rgba(255, 255, 255, 0.03);
    border: 2px solid rgba(59, 130, 246, 0.3);
    border-radius: var(--border-radius-2xl);
    backdrop-filter: blur(20px);
    transition: all var(--transition-slow);
    position: relative;
    overflow: hidden;
}

.primary-method::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: var(--border-radius-2xl) var(--border-radius-2xl) 0 0;
}

.primary-method:hover {
    transform: translateY(-10px);
    border-color: rgba(59, 130, 246, 0.5);
    box-shadow: 0 25px 50px rgba(59, 130, 246, 0.2);
}

.method-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    position: relative;
}

.method-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: var(--border-radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 15px 35px rgba(59, 130, 246, 0.3);
    flex-shrink: 0;
}

.method-icon i {
    font-size: 2rem;
    color: var(--text-primary);
}

.method-info {
    flex: 1;
    text-align: left;
}

.method-info h4 {
    font-family: var(--font-display);
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.method-info p {
    color: var(--text-secondary);
    font-size: 1.1rem;
    margin: 0;
}

.method-badge {
    position: absolute;
    top: -10px;
    right: 0;
    padding: var(--spacing-xs) var(--spacing-md);
    background: var(--gradient-accent);
    color: var(--text-primary);
    font-size: 0.75rem;
    font-weight: 700;
    border-radius: var(--border-radius-xl);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);
}

.method-benefits {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-secondary);
    font-weight: 500;
}

.benefit-item i {
    color: var(--accent-secondary);
    font-size: 1.1rem;
    flex-shrink: 0;
}

/* Primary CTA */
.primary-cta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-xl) var(--spacing-2xl);
    background: var(--gradient-primary);
    border: none;
    border-radius: var(--border-radius-xl);
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 700;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    box-shadow: 0 15px 35px rgba(59, 130, 246, 0.3);
}

.cta-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
}

.cta-text {
    font-size: 1.25rem;
    font-weight: 800;
}

.cta-subtext {
    font-size: 0.9rem;
    font-weight: 500;
    opacity: 0.9;
}

.cta-icon {
    font-size: 1.5rem;
    transition: transform var(--transition-normal);
}

.cta-shine {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.primary-cta:hover .cta-shine {
    left: 100%;
}

.primary-cta:hover .cta-icon {
    transform: translateX(8px);
}

.primary-cta:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 45px rgba(59, 130, 246, 0.4);
}

/* Quick Contact Grid */
.quick-contact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.quick-method {
    padding: var(--spacing-xl);
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-xl);
    text-align: center;
    transition: all var(--transition-normal);
    backdrop-filter: blur(10px);
}

.quick-method:hover {
    transform: translateY(-8px);
    border-color: rgba(16, 185, 129, 0.3);
    box-shadow: 0 15px 30px rgba(16, 185, 129, 0.2);
}

.quick-method .method-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-secondary);
    margin: 0 auto var(--spacing-md);
}

.quick-method h4 {
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.quick-method p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    font-size: 0.95rem;
}

.method-link {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--accent-secondary);
    text-decoration: none;
    font-weight: 600;
    transition: all var(--transition-normal);
}

.method-link:hover {
    gap: var(--spacing-sm);
    color: var(--accent-primary);
}

/* Contact Info Section */
.contact-info-section {
    margin-top: var(--spacing-2xl);
}

.info-title {
    font-family: var(--font-display);
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-primary);
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.contact-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.contact-info-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-lg);
    padding: var(--spacing-xl);
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-xl);
    transition: all var(--transition-normal);
    backdrop-filter: blur(10px);
}

.contact-info-item:hover {
    transform: translateY(-5px);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.info-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
}

.info-icon i {
    font-size: 1.5rem;
    color: var(--text-primary);
}

.info-content h4 {
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.info-content p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
    font-size: 1.1rem;
}

.info-note {
    color: var(--text-muted);
    font-size: 0.875rem;
    font-style: italic;
}

/* Final CTA */
.final-cta {
    margin-top: var(--spacing-3xl);
    padding: var(--spacing-3xl);
    background:
        linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(16, 185, 129, 0.08) 100%),
        rgba(255, 255, 255, 0.02);
    border: 2px solid rgba(59, 130, 246, 0.2);
    border-radius: var(--border-radius-2xl);
    text-align: center;
    backdrop-filter: blur(20px);
    position: relative;
    overflow: hidden;
}

.final-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: var(--border-radius-2xl) var(--border-radius-2xl) 0 0;
}

.final-cta-content h3 {
    font-family: var(--font-display);
    font-size: 2.5rem;
    font-weight: 800;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--spacing-md);
}

.final-cta-content p {
    color: var(--text-secondary);
    font-size: 1.2rem;
    margin-bottom: var(--spacing-xl);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-stat {
    text-align: center;
}

.cta-stat .stat-number {
    font-family: var(--font-display);
    font-size: 1.75rem;
    font-weight: 800;
    color: var(--accent-primary);
    display: block;
    margin-bottom: var(--spacing-xs);
}

.cta-stat .stat-label {
    color: var(--text-muted);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.final-cta-button {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-xl) var(--spacing-3xl);
    background: var(--gradient-primary);
    border: none;
    border-radius: var(--border-radius-xl);
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 800;
    font-size: 1.25rem;
    transition: all var(--transition-normal);
    box-shadow: 0 15px 35px rgba(59, 130, 246, 0.3);
    position: relative;
    overflow: hidden;
}

.final-cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.final-cta-button:hover::before {
    left: 100%;
}

.final-cta-button:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 25px 50px rgba(59, 130, 246, 0.4);
}

/* Advanced Animations and Micro-interactions */

/* Smooth page transitions */
.page-transition {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.page-transition.loaded {
    opacity: 1;
    transform: translateY(0);
}

/* Enhanced button interactions */
.premium-button-effect {
    position: relative;
    overflow: hidden;
    transform-style: preserve-3d;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.premium-button-effect::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s ease;
}

.premium-button-effect:hover::before {
    left: 100%;
}

.premium-button-effect:active {
    transform: scale(0.98);
}

/* Magnetic effect for interactive elements */
.magnetic-element {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.magnetic-element:hover {
    transform: translate3d(0, -2px, 0);
}

/* Parallax scroll effects */
.parallax-element {
    transform: translateZ(0);
    transition: transform 0.1s ease-out;
}

/* Advanced card hover effects */
.premium-card-3d {
    transform-style: preserve-3d;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.premium-card-3d:hover {
    transform: rotateX(5deg) rotateY(5deg) translateZ(20px);
}

/* Staggered animations */
.stagger-animation {
    opacity: 0;
    transform: translateY(30px);
    animation: staggerFadeIn 0.6s ease-out forwards;
}

.stagger-animation:nth-child(1) { animation-delay: 0.1s; }
.stagger-animation:nth-child(2) { animation-delay: 0.2s; }
.stagger-animation:nth-child(3) { animation-delay: 0.3s; }
.stagger-animation:nth-child(4) { animation-delay: 0.4s; }
.stagger-animation:nth-child(5) { animation-delay: 0.5s; }

@keyframes staggerFadeIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Morphing shapes */
.morphing-shape {
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
    animation: morphing 8s ease-in-out infinite;
}

@keyframes morphing {
    0%, 100% {
        border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
    }
    25% {
        border-radius: 58% 42% 75% 25% / 76% 46% 54% 24%;
    }
    50% {
        border-radius: 50% 50% 33% 67% / 55% 27% 73% 45%;
    }
    75% {
        border-radius: 33% 67% 58% 42% / 63% 68% 32% 37%;
    }
}

/* Glitch effect for special elements */
.glitch-effect {
    position: relative;
    animation: glitch 2s infinite;
}

.glitch-effect::before,
.glitch-effect::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.glitch-effect::before {
    animation: glitch-1 0.5s infinite;
    color: #ff0000;
    z-index: -1;
}

.glitch-effect::after {
    animation: glitch-2 0.5s infinite;
    color: #00ff00;
    z-index: -2;
}

@keyframes glitch {
    0%, 100% { transform: translate(0); }
    20% { transform: translate(-2px, 2px); }
    40% { transform: translate(-2px, -2px); }
    60% { transform: translate(2px, 2px); }
    80% { transform: translate(2px, -2px); }
}

@keyframes glitch-1 {
    0%, 100% { transform: translate(0); }
    20% { transform: translate(-1px, 1px); }
    40% { transform: translate(-1px, -1px); }
    60% { transform: translate(1px, 1px); }
    80% { transform: translate(1px, -1px); }
}

@keyframes glitch-2 {
    0%, 100% { transform: translate(0); }
    20% { transform: translate(1px, -1px); }
    40% { transform: translate(1px, 1px); }
    60% { transform: translate(-1px, -1px); }
    80% { transform: translate(-1px, 1px); }
}

/* Typewriter effect */
.typewriter {
    overflow: hidden;
    border-right: 2px solid var(--accent-primary);
    white-space: nowrap;
    animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
    from { width: 0; }
    to { width: 100%; }
}

@keyframes blink-caret {
    from, to { border-color: transparent; }
    50% { border-color: var(--accent-primary); }
}

/* Floating elements */
.floating-element {
    animation: floating 3s ease-in-out infinite;
}

@keyframes floating {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Pulse effect */
.pulse-effect {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Ripple effect */
.ripple-effect {
    position: relative;
    overflow: hidden;
}

.ripple-effect::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.ripple-effect:active::after {
    width: 300px;
    height: 300px;
}

/* Smooth reveal animations */
.reveal-up {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.reveal-up.revealed {
    opacity: 1;
    transform: translateY(0);
}

.reveal-left {
    opacity: 0;
    transform: translateX(-50px);
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.reveal-left.revealed {
    opacity: 1;
    transform: translateX(0);
}

.reveal-right {
    opacity: 0;
    transform: translateX(50px);
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.reveal-right.revealed {
    opacity: 1;
    transform: translateX(0);
}

/* Scale reveal */
.reveal-scale {
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.reveal-scale.revealed {
    opacity: 1;
    transform: scale(1);
}

/* Performance and Mobile Optimizations */

/* Performance optimizations */
* {
    will-change: auto;
}

.hero-section,
.services-section,
.testimonials-section,
.contact-section {
    contain: layout style paint;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .floating-element,
    .pulse-effect,
    .morphing-shape {
        animation: none !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --text-primary: #ffffff;
        --text-secondary: #e0e0e0;
        --text-muted: #c0c0c0;
        --bg-primary: #000000;
        --bg-secondary: #1a1a1a;
        --accent-primary: #00aaff;
        --accent-secondary: #00ff88;
    }
}

/* Dark mode optimizations */
@media (prefers-color-scheme: dark) {
    :root {
        --shadow-sm: 0 1px 2px 0 rgba(255, 255, 255, 0.05);
        --shadow-md: 0 4px 6px -1px rgba(255, 255, 255, 0.1);
        --shadow-lg: 0 10px 15px -3px rgba(255, 255, 255, 0.1);
    }
}

/* Mobile-first responsive design */
@media (max-width: 1024px) {
    /* Tablet optimizations */
    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--spacing-2xl);
    }

    .hero-visual {
        order: -1;
    }

    .hero-showcase {
        width: 300px;
        height: 300px;
    }

    .services-grid {
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
    }

    .urgency-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--spacing-md);
    }

    .timer-display {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    /* Mobile optimizations */
    :root {
        --spacing-xs: 0.375rem;
        --spacing-sm: 0.75rem;
        --spacing-md: 1rem;
        --spacing-lg: 1.25rem;
        --spacing-xl: 1.5rem;
        --spacing-2xl: 2rem;
        --spacing-3xl: 2.5rem;
    }

    /* Typography adjustments */
    .hero-title {
        font-size: clamp(var(--text-3xl), 8vw, var(--text-5xl));
    }

    .section-title {
        font-size: clamp(var(--text-2xl), 6vw, var(--text-4xl));
    }

    .display-text {
        font-size: clamp(var(--text-4xl), 10vw, var(--text-6xl));
    }

    /* Hero section mobile */
    .hero-stats {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: center;
    }

    .hero-actions {
        align-items: center;
    }

    .primary-action {
        width: 100%;
    }

    .premium-cta {
        width: 100%;
        min-width: auto;
    }

    /* Services mobile */
    .services-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .method-header {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }

    .method-info {
        text-align: center;
    }

    .quick-contact-grid {
        grid-template-columns: 1fr;
    }

    /* Contact mobile */
    .contact-info-grid {
        grid-template-columns: 1fr;
    }

    .cta-stats {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    /* Trust signals mobile */
    .trust-metrics {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
    }

    .trust-badges {
        grid-template-columns: 1fr;
    }

    /* Testimonials mobile */
    .testimonials-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
}

@media (max-width: 480px) {
    /* Small mobile optimizations */
    :root {
        --spacing-xs: 0.25rem;
        --spacing-sm: 0.5rem;
        --spacing-md: 0.75rem;
        --spacing-lg: 1rem;
        --spacing-xl: 1.25rem;
        --spacing-2xl: 1.5rem;
        --spacing-3xl: 2rem;
    }

    /* Ultra-compact mobile */
    .hero-title {
        font-size: clamp(var(--text-2xl), 10vw, var(--text-4xl));
        line-height: var(--leading-tight);
    }

    .section-title {
        font-size: clamp(var(--text-xl), 8vw, var(--text-3xl));
    }

    .hero-trust-signals {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: center;
    }

    .trust-item {
        font-size: var(--text-xs);
    }

    .trust-metrics {
        grid-template-columns: 1fr;
    }

    .timer-display {
        flex-direction: column;
        gap: var(--spacing-xs);
    }

    .timer-unit {
        min-width: auto;
        padding: var(--spacing-xs);
    }

    /* Button adjustments */
    .premium-cta {
        padding: var(--spacing-md) var(--spacing-lg);
        flex-direction: column;
        gap: var(--spacing-xs);
        text-align: center;
    }

    .final-cta-button {
        padding: var(--spacing-lg) var(--spacing-xl);
        font-size: var(--text-lg);
    }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
    /* Remove hover effects on touch devices */
    .service-card:hover,
    .testimonial-card:hover,
    .metric-card:hover,
    .trust-badge:hover {
        transform: none;
        box-shadow: none;
    }

    /* Increase touch targets */
    .primary-button,
    .secondary-button,
    .method-link,
    .nav-link {
        min-height: 44px;
        padding: var(--spacing-md) var(--spacing-lg);
    }

    /* Simplify animations */
    .floating-element,
    .pulse-effect {
        animation: none;
    }
}

/* Print styles */
@media print {
    * {
        background: transparent !important;
        color: black !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }

    .hero-background,
    .services-background,
    .testimonials-background,
    .contact-background {
        display: none !important;
    }

    .primary-button,
    .secondary-button {
        border: 1px solid black !important;
    }
}

/* Loading performance optimizations */
.lazy-load {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.lazy-load.loaded {
    opacity: 1;
}

/* Critical CSS inlining hint */
.above-fold {
    contain: layout style paint;
}

/* GPU acceleration for smooth animations */
.gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* Additional responsive styles */
@media (max-width: 1024px) {
    .about-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
        text-align: center;
    }

    .about-stats {
        grid-template-columns: 1fr;
        max-width: 400px;
        margin: var(--spacing-lg) auto;
    }

    .tech-stack {
        grid-template-columns: repeat(2, 1fr);
        max-width: 400px;
        margin: 0 auto;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }
}

@media (max-width: 768px) {
    .about-section,
    .contact-section {
        padding: var(--spacing-xl) 0;
    }

    .portfolio-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .stat-card {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-xs);
        padding: var(--spacing-sm);
    }

    .tech-stack {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-sm);
    }

    .contact-item {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-md);
    }

    .contact-content {
        gap: var(--spacing-lg);
        margin-top: var(--spacing-lg);
    }
}

/* Form Message Styles */
.form-message {
    margin-top: var(--spacing-lg);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    text-align: center;
    font-weight: 600;
    transition: all 0.3s ease;
}

.form-message.success {
    background: rgba(34, 197, 94, 0.1);
    border: 1px solid rgba(34, 197, 94, 0.3);
    color: #22c55e;
}

.form-message.error {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: #ef4444;
}

.form-message.success i {
    color: #22c55e;
    margin-right: var(--spacing-sm);
}

.form-message.error i {
    color: #ef4444;
    margin-right: var(--spacing-sm);
}

/* Loading state for submit button */
.primary-button.loading {
    opacity: 0.7;
    cursor: not-allowed;
}

.primary-button.loading .loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: var(--spacing-sm);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Contact Action Section */
.contact-action {
    display: flex;
    align-items: center;
    justify-content: center;
}

.contact-cta {
    text-align: center;
    padding: var(--spacing-2xl);
    background: var(--bg-glass);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-lg);
    backdrop-filter: blur(10px);
    max-width: 500px;
    width: 100%;
}

.contact-cta h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.contact-cta p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    line-height: 1.6;
}

.email-button {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg) var(--spacing-xl);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: var(--spacing-xl);
    transition: all var(--transition-normal);
}

.email-button:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.contact-benefits {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    align-items: center;
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.benefit-item i {
    color: var(--accent-primary);
    font-size: 0.8rem;
}

/* Mobile adjustments */
@media (max-width: 768px) {
    .contact-cta {
        padding: var(--spacing-lg);
        margin: 0 var(--spacing-md);
    }

    .contact-benefits {
        flex-direction: column;
        gap: var(--spacing-xs);
    }

    .tech-stack {
        grid-template-columns: 1fr;
        gap: var(--spacing-xs);
    }

    .tech-item {
        padding: var(--spacing-sm);
    }
}

/* Additional mobile optimizations for very small screens */
@media (max-width: 480px) {
    .nav-container {
        padding: 0 var(--spacing-md);
    }

    .section-container {
        padding: 0 var(--spacing-md);
    }

    .contact-cta {
        padding: var(--spacing-md);
        margin: 0 var(--spacing-sm);
    }

    .about-stats {
        grid-template-columns: 1fr;
        gap: var(--spacing-xs);
        margin: var(--spacing-md) auto;
    }

    .stat-card {
        padding: var(--spacing-xs);
    }

    .contact-item {
        padding: var(--spacing-sm);
    }

    .contact-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
}
