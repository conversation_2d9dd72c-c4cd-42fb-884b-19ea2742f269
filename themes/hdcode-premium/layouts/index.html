{{ define "main" }}

<!-- Hero Section -->
<section id="hero" class="hero-section">
    <div class="hero-background">
        <div class="hero-grid"></div>
        <div class="hero-particles"></div>
        <div class="hero-gradient-orbs"></div>
        <div class="hero-floating-elements"></div>
    </div>

    <div class="hero-container">
        <div class="hero-content">
            <!-- Premium Badge with Animation -->
            <div class="hero-badge-container">
                <div class="hero-badge">
                    <div class="badge-glow"></div>
                    <i class="fas fa-crown"></i>
                    <span>{{ .Site.Params.hero.badge }}</span>
                    <div class="badge-shine"></div>
                </div>
            </div>

            <!-- Enhanced Title with Premium Typography -->
            <h1 class="hero-title">
                <span class="title-line title-line-1">{{ .Site.Params.hero.title_line1 }}</span>
                <span class="title-line title-line-2 gradient-text">{{ .Site.Params.hero.title_line2 }}</span>
                <span class="title-line title-line-3">with <span class="hdcode-animated" id="hdcode-text">HDCode</span></span>
            </h1>

            <!-- Value Proposition with Trust Signals -->
            <div class="hero-value-prop">
                <p class="hero-description">
                    {{ .Site.Params.hero.description }}
                </p>
                <div class="hero-trust-signals">
                    <div class="trust-item">
                        <i class="fas fa-shield-check"></i>
                        <span>Enterprise-Grade Security</span>
                    </div>
                    <div class="trust-item">
                        <i class="fas fa-rocket"></i>
                        <span>Lightning-Fast Delivery</span>
                    </div>
                    <div class="trust-item">
                        <i class="fas fa-award"></i>
                        <span>Award-Winning Design</span>
                    </div>
                </div>
            </div>

            <!-- Enhanced Action Buttons with Urgency -->
            <div class="hero-actions">
                <div class="primary-action">
                    <a href="#contact" class="primary-button premium-cta">
                        <div class="button-bg"></div>
                        <div class="button-content">
                            <span class="button-text">{{ .Site.Params.hero.primary_button_text }}</span>
                            <span class="button-subtext">Free Consultation</span>
                        </div>
                        <i class="fas fa-arrow-right button-icon"></i>
                        <div class="button-shine"></div>
                    </a>
                    <div class="cta-urgency">
                        <i class="fas fa-clock"></i>
                        <span>Limited slots available this month</span>
                    </div>
                </div>

                <div class="secondary-action">
                    <a href="#portfolio" class="secondary-button premium-secondary">
                        <i class="fas fa-play"></i>
                        <span>{{ .Site.Params.hero.secondary_button_text }}</span>
                        <div class="button-hover-effect"></div>
                    </a>
                </div>
            </div>

            <!-- Social Proof Stats -->
            <div class="hero-stats">
                {{ range .Site.Params.hero.stats }}
                <div class="stat-item">
                    <div class="stat-number" data-target="{{ index $.Site.Params.global_stats .number_key }}">0</div>
                    <div class="stat-label">{{ .label }}</div>
                    <div class="stat-glow"></div>
                </div>
                {{ end }}
            </div>
        </div>

        <!-- Premium Visual Element -->
        <div class="hero-visual">
            <div class="hero-showcase">
                <div class="showcase-card showcase-mobile">
                    <div class="card-glow"></div>
                    <div class="card-content">
                        <i class="fab fa-android"></i>
                        <span>Mobile Apps</span>
                    </div>
                </div>
                <div class="showcase-card showcase-web">
                    <div class="card-glow"></div>
                    <div class="card-content">
                        <i class="fas fa-globe"></i>
                        <span>Web Solutions</span>
                    </div>
                </div>
                <div class="showcase-card showcase-mvp">
                    <div class="card-glow"></div>
                    <div class="card-content">
                        <i class="fas fa-rocket"></i>
                        <span>MVP Development</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Services Section -->
<section id="services" class="services-section">
    <div class="services-background">
        <div class="services-gradient-orbs"></div>
        <div class="services-grid-pattern"></div>
    </div>

    <div class="section-container">
        <div class="section-header">
            <div class="section-badge premium-badge">
                <i class="fas fa-star"></i>
                <span>{{ .Site.Params.services.badge }}</span>
            </div>
            <h2 class="section-title">{{ .Site.Params.services.title }}</h2>
            <p class="section-description">
                {{ .Site.Params.services.description }}
            </p>
        </div>

        <div class="services-grid">
            {{ range $index, $service := .Site.Params.services.items }}
            <div class="service-card{{ if .featured }} featured{{ end }}" data-index="{{ $index }}">
                <div class="card-background"></div>
                <div class="card-glow-effect"></div>

                {{ if .featured }}
                <div class="service-badge premium-service-badge">
                    <i class="fas fa-crown"></i>
                    <span>{{ .badge }}</span>
                    <div class="badge-sparkle"></div>
                </div>
                {{ end }}

                <div class="service-header">
                    <div class="service-icon-container">
                        <div class="icon-background"></div>
                        <div class="service-icon">
                            <i class="{{ .icon }}"></i>
                        </div>
                        <div class="icon-glow"></div>
                    </div>
                    <div class="service-meta">
                        <h3 class="service-title">{{ .title }}</h3>
                        {{ if .featured }}
                        <div class="service-popularity">
                            <div class="popularity-stars">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                            <span class="popularity-text">Most Requested</span>
                        </div>
                        {{ end }}
                    </div>
                </div>

                <div class="service-content">
                    <p class="service-description">
                        {{ .description }}
                    </p>

                    <div class="service-features">
                        <h4 class="features-title">What's Included:</h4>
                        <ul class="features-list">
                            {{ range .features }}
                            <li>
                                <i class="fas fa-check-circle"></i>
                                <span>{{ . }}</span>
                            </li>
                            {{ end }}
                        </ul>
                    </div>

                    <div class="service-value">
                        <div class="value-proposition">
                            {{ if .featured }}
                            <div class="value-highlight">
                                <i class="fas fa-bolt"></i>
                                <span>Fast-track to market leadership</span>
                            </div>
                            {{ else }}
                            <div class="value-highlight">
                                <i class="fas fa-rocket"></i>
                                <span>Professional grade solution</span>
                            </div>
                            {{ end }}
                        </div>

                        <div class="service-timeline">
                            <i class="fas fa-clock"></i>
                            <span>{{ if .featured }}2-4 weeks{{ else }}3-6 weeks{{ end }} delivery</span>
                        </div>
                    </div>
                </div>

                <div class="service-action">
                    <a href="#contact" class="service-link premium-service-cta">
                        <div class="cta-content">
                            <span class="cta-text">{{ $.Site.Params.services.learn_more_text }}</span>
                            <span class="cta-subtext">Free consultation</span>
                        </div>
                        <i class="fas fa-arrow-right cta-icon"></i>
                        <div class="cta-shine"></div>
                    </a>

                    <div class="service-guarantee">
                        <i class="fas fa-shield-check"></i>
                        <span>100% satisfaction guarantee</span>
                    </div>
                </div>
            </div>
            {{ end }}
        </div>

        <!-- Services CTA Section -->
        <div class="services-cta">
            <div class="cta-content">
                <h3>Not sure which service fits your needs?</h3>
                <p>Get a free consultation and we'll recommend the perfect solution for your business goals.</p>
                <a href="#contact" class="primary-button services-cta-button">
                    <span>Get Free Consultation</span>
                    <i class="fas fa-comments"></i>
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Testimonials Section -->
<section id="testimonials" class="testimonials-section">
    <div class="testimonials-background">
        <div class="testimonials-gradient-orbs"></div>
        <div class="testimonials-pattern"></div>
    </div>

    <div class="section-container">
        <div class="section-header">
            <div class="section-badge premium-badge">
                <i class="fas fa-quote-left"></i>
                <span>{{ .Site.Params.testimonials.badge }}</span>
            </div>
            <h2 class="section-title">{{ .Site.Params.testimonials.title }}</h2>
            <p class="section-description">
                {{ .Site.Params.testimonials.description }}
            </p>
        </div>

        <div class="testimonials-grid">
            {{ range $index, $testimonial := .Site.Params.testimonials.items }}
            <div class="testimonial-card" data-index="{{ $index }}">
                <div class="testimonial-background"></div>
                <div class="testimonial-glow"></div>

                <div class="testimonial-header">
                    <div class="testimonial-quote">
                        <i class="fas fa-quote-left"></i>
                    </div>
                    <div class="testimonial-rating">
                        {{ range $i := (seq $testimonial.rating) }}
                        <i class="fas fa-star"></i>
                        {{ end }}
                    </div>
                </div>

                <div class="testimonial-content">
                    <p class="testimonial-text">{{ $testimonial.text }}</p>

                    <div class="testimonial-result">
                        <div class="result-highlight">
                            <i class="fas fa-chart-line"></i>
                            <span>{{ $testimonial.result }}</span>
                        </div>
                        <div class="project-type">{{ $testimonial.project }}</div>
                    </div>
                </div>

                <div class="testimonial-author">
                    <div class="author-avatar">
                        <div class="avatar-placeholder">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="avatar-glow"></div>
                    </div>
                    <div class="author-info">
                        <h4 class="author-name">{{ $testimonial.name }}</h4>
                        <p class="author-position">{{ $testimonial.position }}</p>
                        <p class="author-company">{{ $testimonial.company }}</p>
                    </div>
                </div>
            </div>
            {{ end }}
        </div>
    </div>
</section>

<!-- Trust Signals Section -->
<section id="trust-signals" class="trust-signals-section">
    <div class="section-container">
        <div class="section-header">
            <div class="section-badge premium-badge">
                <i class="fas fa-shield-check"></i>
                <span>{{ .Site.Params.trust_signals.badge }}</span>
            </div>
            <h2 class="section-title">{{ .Site.Params.trust_signals.title }}</h2>
            <p class="section-description">
                {{ .Site.Params.trust_signals.description }}
            </p>
        </div>

        <!-- Trust Metrics -->
        <div class="trust-metrics">
            {{ range $index, $metric := .Site.Params.trust_signals.metrics }}
            <div class="metric-card" data-index="{{ $index }}">
                <div class="metric-icon">
                    <i class="{{ $metric.icon }}"></i>
                </div>
                <div class="metric-number" data-target="{{ $metric.number }}">0</div>
                <div class="metric-label">{{ $metric.label }}</div>
                <div class="metric-glow"></div>
            </div>
            {{ end }}
        </div>

        <!-- Trust Badges -->
        <div class="trust-badges">
            {{ range $index, $badge := .Site.Params.trust_signals.badges }}
            <div class="trust-badge" data-index="{{ $index }}">
                <div class="badge-icon">
                    <i class="{{ $badge.icon }}"></i>
                </div>
                <div class="badge-content">
                    <h4 class="badge-name">{{ $badge.name }}</h4>
                    <p class="badge-description">{{ $badge.description }}</p>
                </div>
                <div class="badge-verification">
                    <i class="fas fa-check-circle"></i>
                </div>
            </div>
            {{ end }}
        </div>
    </div>
</section>

<!-- Portfolio Section -->
{{ if .Site.Params.portfolio.enabled }}
<section id="portfolio" class="portfolio-section">
    <div class="section-container">
        <div class="section-header">
            <div class="section-badge">{{ .Site.Params.portfolio.badge }}</div>
            <h2 class="section-title">{{ .Site.Params.portfolio.title }}</h2>
            <p class="section-description">
                {{ .Site.Params.portfolio.description }}
            </p>
        </div>

        <div class="portfolio-grid">
            {{ range .Site.Params.portfolio.items }}
            <div class="portfolio-item">
                <div class="portfolio-image">
                    <div class="portfolio-preview">
                        <i class="{{ .icon }}"></i>
                        <div class="preview-text">
                            <h4>{{ .preview_title }}</h4>
                            <span>{{ .preview_subtitle }}</span>
                        </div>
                    </div>
                    <div class="portfolio-overlay">
                        <div class="portfolio-info">
                            <h3>{{ .title }}</h3>
                            <p>{{ .description }}</p>
                            <div class="portfolio-tech">
                                {{ range .technologies }}
                                <span>{{ . }}</span>
                                {{ end }}
                            </div>
                        </div>
                        <div class="portfolio-actions">
                            {{ if .demo_link }}
                            <a href="{{ .demo_link }}" class="portfolio-link">
                                <i class="fas fa-external-link-alt"></i>
                            </a>
                            {{ end }}
                            {{ if .github_link }}
                            <a href="{{ .github_link }}" class="portfolio-link">
                                <i class="fab fa-github"></i>
                            </a>
                            {{ end }}
                            {{ if .app_store_link }}
                            <a href="{{ .app_store_link }}" class="portfolio-link">
                                <i class="fab fa-app-store"></i>
                            </a>
                            {{ end }}
                            {{ if .play_store_link }}
                            <a href="{{ .play_store_link }}" class="portfolio-link">
                                <i class="fab fa-google-play"></i>
                            </a>
                            {{ end }}
                        </div>
                    </div>
                </div>
            </div>
            {{ end }}
        </div>

        <div class="portfolio-cta">
            <a href="#contact" class="primary-button">
                <span>{{ .Site.Params.portfolio.cta_text }}</span>
                <i class="fas fa-arrow-right"></i>
            </a>
        </div>
    </div>
</section>
{{ end }}

<!-- Experience Domains Section -->
<section id="experience" class="experience-section">
    <div class="experience-background">
        <div class="experience-particles"></div>
        <div class="experience-orbs"></div>
    </div>

    <div class="section-container">
        <div class="section-header">
            <div class="section-badge">{{ .Site.Params.experience.badge }}</div>
            <h2 class="section-title">{{ .Site.Params.experience.title }}</h2>
            <p class="section-description">
                {{ .Site.Params.experience.description }}
            </p>
        </div>

        <div class="experience-grid">
            {{ range $index, $domain := .Site.Params.experience.domains }}
            <div class="experience-card" data-index="{{ $index }}">
                <div class="experience-card-background"></div>
                <div class="experience-card-content">
                    <div class="experience-icon-container">
                        <div class="experience-icon-bg" style="background: linear-gradient(135deg, {{ $domain.color }}20, {{ $domain.color }}10);"></div>
                        <div class="experience-icon" style="color: {{ $domain.color }};">
                            <i class="{{ $domain.icon }}"></i>
                        </div>
                    </div>
                    <h3 class="experience-name">{{ $domain.name }}</h3>
                    <div class="experience-glow" style="background: radial-gradient(circle, {{ $domain.color }}15 0%, transparent 70%);"></div>
                </div>
            </div>
            {{ end }}
        </div>
    </div>
</section>

<!-- About Section -->
<section id="about" class="about-section">
    <div class="section-container">
        <div class="about-content">
            <div class="about-text">
                <div class="section-badge">{{ .Site.Params.about.badge }}</div>
                <h2 class="section-title">{{ .Site.Params.about.title }}</h2>
                <p class="about-description">
                    {{ .Site.Params.about.description }}
                </p>

                <div class="about-stats">
                    {{ range .Site.Params.about.stats }}
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="{{ .icon }}"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number">{{ index $.Site.Params.global_stats .number_key }}</div>
                            <div class="stat-label">{{ .label }}</div>
                        </div>
                    </div>
                    {{ end }}
                </div>

                <a href="#contact" class="secondary-button">
                    <span>{{ .Site.Params.about.cta_text }}</span>
                    <i class="fas fa-arrow-right"></i>
                </a>
            </div>

            <div class="about-visual">
                <div class="tech-stack">
                    {{ range .Site.Params.about.technologies }}
                    <div class="tech-item">
                        <i class="{{ .icon }}"></i>
                        <span>{{ .name }}</span>
                    </div>
                    {{ end }}
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Contact Section -->
<section id="contact" class="contact-section">
    <div class="contact-background">
        <div class="contact-gradient-orbs"></div>
        <div class="contact-particles"></div>
    </div>

    <div class="section-container">
        <div class="section-header">
            <div class="section-badge premium-badge">
                <i class="fas fa-rocket"></i>
                <span>{{ .Site.Params.contact.badge }}</span>
            </div>
            <h2 class="section-title">{{ .Site.Params.contact.title }}</h2>
            <p class="section-description">
                {{ .Site.Params.contact.description }}
            </p>
        </div>

        <!-- Urgency Banner -->
        <div class="urgency-banner">
            <div class="urgency-content">
                <div class="urgency-icon">
                    <i class="fas fa-fire"></i>
                </div>
                <div class="urgency-text">
                    <h4>Limited Time Offer</h4>
                    <p>Book your free consultation this month and get 20% off your first project</p>
                </div>
                <div class="urgency-timer">
                    <span class="timer-label">Offer expires in:</span>
                    <div class="timer-display">
                        <div class="timer-unit">
                            <span class="timer-number" id="days">15</span>
                            <span class="timer-text">Days</span>
                        </div>
                        <div class="timer-unit">
                            <span class="timer-number" id="hours">23</span>
                            <span class="timer-text">Hours</span>
                        </div>
                        <div class="timer-unit">
                            <span class="timer-number" id="minutes">45</span>
                            <span class="timer-text">Min</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="contact-content">
            <!-- Contact Options -->
            <div class="contact-options">
                <h3 class="options-title">Choose Your Preferred Way to Connect</h3>

                <div class="contact-methods">
                    <!-- Primary CTA - Email -->
                    <div class="contact-method primary-method">
                        <div class="method-header">
                            <div class="method-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="method-info">
                                <h4>Email Consultation</h4>
                                <p>Perfect for detailed project discussions</p>
                            </div>
                            <div class="method-badge">
                                <span>Most Popular</span>
                            </div>
                        </div>

                        <div class="method-benefits">
                            <div class="benefit-item">
                                <i class="fas fa-check-circle"></i>
                                <span>Free 30-minute consultation</span>
                            </div>
                            <div class="benefit-item">
                                <i class="fas fa-check-circle"></i>
                                <span>Detailed project proposal</span>
                            </div>
                            <div class="benefit-item">
                                <i class="fas fa-check-circle"></i>
                                <span>24-hour response guarantee</span>
                            </div>
                        </div>

                        <a href="mailto:{{ .Site.Params.email }}?subject=Project Inquiry - {{ .Site.Title }}&body=Hi HDCode Team,%0D%0A%0D%0AI'm interested in discussing a project with you.%0D%0A%0D%0AProject Type: %0D%0ATimeline: %0D%0ABudget Range: %0D%0A%0D%0AProject Details:%0D%0A%0D%0A%0D%0ABest regards,"
                           class="method-cta primary-cta">
                            <div class="cta-content">
                                <span class="cta-text">Start Your Project</span>
                                <span class="cta-subtext">Free consultation included</span>
                            </div>
                            <i class="fas fa-arrow-right cta-icon"></i>
                            <div class="cta-shine"></div>
                        </a>
                    </div>

                    <!-- Quick Contact Options -->
                    <div class="quick-contact-grid">
                        <div class="contact-method quick-method">
                            <div class="method-icon">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                            <h4>Schedule a Call</h4>
                            <p>15-minute discovery call</p>
                            <a href="#" class="method-link">
                                <span>Book Now</span>
                                <i class="fas fa-external-link-alt"></i>
                            </a>
                        </div>

                        <div class="contact-method quick-method">
                            <div class="method-icon">
                                <i class="fas fa-comments"></i>
                            </div>
                            <h4>Live Chat</h4>
                            <p>Instant answers to your questions</p>
                            <a href="#" class="method-link">
                                <span>Chat Now</span>
                                <i class="fas fa-comment-dots"></i>
                            </a>
                        </div>

                        <div class="contact-method quick-method">
                            <div class="method-icon">
                                <i class="fas fa-file-download"></i>
                            </div>
                            <h4>Project Planner</h4>
                            <p>Free project planning template</p>
                            <a href="#" class="method-link">
                                <span>Download</span>
                                <i class="fas fa-download"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="contact-info-section">
                <h3 class="info-title">Get in Touch</h3>

                <div class="contact-info-grid">
                    <div class="contact-info-item">
                        <div class="info-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="info-content">
                            <h4>{{ .Site.Params.contact.email_label }}</h4>
                            <p>{{ .Site.Params.email }}</p>
                            <span class="info-note">Response within 2 hours</span>
                        </div>
                    </div>

                    <div class="contact-info-item">
                        <div class="info-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="info-content">
                            <h4>{{ .Site.Params.contact.availability_label }}</h4>
                            <p>{{ .Site.Params.contact.availability }}</p>
                            <span class="info-note">Global timezone support</span>
                        </div>
                    </div>

                    <div class="contact-info-item">
                        <div class="info-icon">
                            <i class="fas fa-shield-check"></i>
                        </div>
                        <div class="info-content">
                            <h4>Privacy Guaranteed</h4>
                            <p>Your project details are 100% confidential</p>
                            <span class="info-note">NDA available upon request</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Final CTA -->
        <div class="final-cta">
            <div class="final-cta-content">
                <h3>Ready to Transform Your Business?</h3>
                <p>Join 100+ successful companies who chose HDCode for their digital transformation</p>

                <div class="cta-stats">
                    <div class="cta-stat">
                        <span class="stat-number">98%</span>
                        <span class="stat-label">Client Satisfaction</span>
                    </div>
                    <div class="cta-stat">
                        <span class="stat-number">2-4 weeks</span>
                        <span class="stat-label">Average Delivery</span>
                    </div>
                    <div class="cta-stat">
                        <span class="stat-number">$50M+</span>
                        <span class="stat-label">Client Revenue Generated</span>
                    </div>
                </div>

                <a href="mailto:{{ .Site.Params.email }}?subject=Project Inquiry - {{ .Site.Title }}&body=Hi HDCode Team,%0D%0A%0D%0AI'm ready to start my project with you.%0D%0A%0D%0AProject Type: %0D%0ATimeline: %0D%0ABudget Range: %0D%0A%0D%0AProject Details:%0D%0A%0D%0A%0D%0ABest regards,"
                   class="final-cta-button">
                    <span>Start Your Project Today</span>
                    <i class="fas fa-rocket"></i>
                </a>
            </div>
        </div>
    </div>
</section>

{{ end }}
